import React, { useState, useRef, useEffect } from 'react';
import { X, UploadCloud, FileText } from 'lucide-react';

import { Button } from '../ui/button';

import { toast } from '@/components/ui/use-toast';
import { axiosInstance } from '@/lib/axiosinstance';

const allowedCoverLetterFormats = [
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
];
const maxCoverLetterSize = 5 * 1024 * 1024; // 5MB

const CoverLetterUpload: React.FC = () => {
  const [selectedCoverLetter, setSelectedCoverLetter] = useState<File | null>(
    null,
  );
  const [uploadedFileName, setUploadedFileName] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [coverLetterPreviewURL, setCoverLetterPreviewURL] = useState<
    string | null
  >(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const truncateFileName = (fileName?: string) => {
    if (!fileName) return ''; // Handle undefined values
    const maxLength = 20;
    const extension = fileName.includes('.')
      ? fileName.substring(fileName.lastIndexOf('.'))
      : ''; // Handle files without extension
    return fileName.length > maxLength
      ? `${fileName.substring(0, maxLength - extension.length)}...${extension}`
      : fileName;
  };

  const handleCoverLetterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (allowedCoverLetterFormats.includes(file.type)) {
        if (file.size <= maxCoverLetterSize) {
          setSelectedCoverLetter(file);
          setUploadedFileName(file.name);

          // Create a preview URL only for PDFs
          if (file.type === 'application/pdf') {
            const fileURL = URL.createObjectURL(file);
            setCoverLetterPreviewURL(fileURL);
          } else {
            setCoverLetterPreviewURL(null);
          }
        } else {
          toast({
            variant: 'destructive',
            title: 'File too large',
            description: 'Cover letter size should not exceed 5MB.',
          });
        }
      } else {
        toast({
          variant: 'destructive',
          title: 'Invalid file type',
          description: 'Supported formats: PDF, DOCX.',
        });
      }
    }
  };

  const handleUploadClick = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedCoverLetter) {
      toast({
        variant: 'destructive',
        title: 'No Cover Letter Selected',
        description: 'Please select a cover letter before uploading.',
      });
      return;
    }

    // Additional validation
    if (!allowedCoverLetterFormats.includes(selectedCoverLetter.type)) {
      toast({
        variant: 'destructive',
        title: 'Invalid File Type',
        description: 'Please select a PDF or DOCX file.',
      });
      return;
    }

    if (selectedCoverLetter.size > maxCoverLetterSize) {
      toast({
        variant: 'destructive',
        title: 'File Too Large',
        description: 'Cover letter size should not exceed 5MB.',
      });
      return;
    }

    const formData = new FormData();
    formData.append('file', selectedCoverLetter);

    console.log('Uploading cover letter:', {
      fileName: selectedCoverLetter.name,
      fileSize: selectedCoverLetter.size,
      fileType: selectedCoverLetter.type,
    });

    try {
      setIsUploading(true);
      const postResponse = await axiosInstance.post(
        '/register/upload-image',
        formData,
        { headers: { 'Content-Type': 'multipart/form-data' } },
      );

      console.log('Upload response:', postResponse.data);

      const { Location } = postResponse.data.data;

      if (!Location) {
        console.error('No Location in response:', postResponse.data);
        throw new Error('Failed to upload the cover letter - no URL returned.');
      }

      console.log(
        'Updating freelancer profile with cover letter URL:',
        Location,
      );

      const putResponse = await axiosInstance.put(`/freelancer`, {
        coverLetter: Location,
      });

      console.log('Profile update response:', putResponse.data);

      if (putResponse.status === 200) {
        setUploadedFileName(selectedCoverLetter.name);
        toast({
          title: 'Success',
          description: 'Cover letter uploaded successfully!',
        });
      } else {
        console.error('Profile update failed:', putResponse);
        throw new Error('Failed to update cover letter in profile.');
      }
    } catch (error: any) {
      console.error('Cover letter upload error:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      toast({
        variant: 'destructive',
        title: 'Error',
        description:
          error.response?.data?.message ||
          'Something went wrong. Please try again.',
      });
    } finally {
      setIsUploading(false);
    }
  };

  useEffect(() => {
    const fetchCoverLetter = async () => {
      try {
        const response = await axiosInstance.get('/freelancer'); // API to get user profile
        console.log('Fetched freelancer data:', response.data);
        if (response.data.coverLetter) {
          setUploadedFileName(response.data.coverLetter); // Set the stored cover letter URL
          setCoverLetterPreviewURL(response.data.coverLetter); // Set the preview URL
        }
      } catch (error) {
        console.error('Error fetching cover letter:', error);
      }
    };

    fetchCoverLetter();
  }, []);

  const handleCancelClick = () => {
    setSelectedCoverLetter(null);
    setCoverLetterPreviewURL(null);
    setUploadedFileName(null);
  };

  return (
    <div className="upload-form max-w-md mx-auto rounded shadow-md p-4">
      <div className="space-y-6 flex flex-col items-center">
        <div
          className="flex flex-col items-center justify-center border-dashed border-2 border-gray-400 rounded-lg p-6 w-full cursor-pointer"
          onClick={() => fileInputRef.current?.click()}
        >
          {selectedCoverLetter ? (
            <div className="w-full flex flex-col items-center gap-4 text-gray-700 text-center">
              <div className="flex flex-1 gap-6">
                <p className="truncate">
                  {truncateFileName(selectedCoverLetter.name)}
                </p>
                <button
                  className="bg-red-600 text-white rounded-full p-1 hover:bg-red-700"
                  onClick={handleCancelClick}
                  aria-label="Remove file"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              {/* Preview Section */}
              {coverLetterPreviewURL ? (
                <iframe
                  src={coverLetterPreviewURL}
                  title="Cover Letter Preview"
                  className="w-full h-40 border rounded"
                />
              ) : (
                <div className="flex items-center space-x-2 p-2 bg-gray-100 rounded">
                  <FileText className="text-gray-500 w-6 h-6" />
                  <span className="text-gray-600 text-sm">
                    {truncateFileName(selectedCoverLetter.name)}
                  </span>
                </div>
              )}
            </div>
          ) : (
            <>
              <UploadCloud className="text-gray-500 w-12 h-12 mb-2" />
              <p className="text-gray-700 text-center">
                Drag and drop your cover letter here or click to upload
              </p>
              <div className="flex items-center mt-2">
                <span className="text-gray-600 text-xs md:text-sm">
                  Supported formats: PDF, DOCX.
                </span>
              </div>
              <input
                type="file"
                accept={allowedCoverLetterFormats.join(',')}
                onChange={handleCoverLetterChange}
                className="hidden"
                ref={fileInputRef}
              />
            </>
          )}
        </div>

        {selectedCoverLetter && (
          <Button
            onClick={handleUploadClick}
            className="w-full"
            disabled={isUploading}
          >
            {isUploading ? 'Uploading...' : 'Upload Cover Letter'}
          </Button>
        )}

        {uploadedFileName && (
          <p className="text-center text-gray-600">
            Uploaded:{' '}
            <strong>{truncateFileName(uploadedFileName || '')}</strong>
          </p>
        )}
      </div>
    </div>
  );
};

export default CoverLetterUpload;
