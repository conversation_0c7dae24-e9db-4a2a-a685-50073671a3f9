"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/faye-websocket";
exports.ids = ["vendor-chunks/faye-websocket"];
exports.modules = {

/***/ "(ssr)/./node_modules/faye-websocket/lib/faye/eventsource.js":
/*!*************************************************************!*\
  !*** ./node_modules/faye-websocket/lib/faye/eventsource.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Stream      = (__webpack_require__(/*! stream */ \"stream\").Stream),\n    util        = __webpack_require__(/*! util */ \"util\"),\n    driver      = __webpack_require__(/*! websocket-driver */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver.js\"),\n    Headers     = __webpack_require__(/*! websocket-driver/lib/websocket/driver/headers */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/headers.js\"),\n    API         = __webpack_require__(/*! ./websocket/api */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api.js\"),\n    EventTarget = __webpack_require__(/*! ./websocket/api/event_target */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event_target.js\"),\n    Event       = __webpack_require__(/*! ./websocket/api/event */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event.js\");\n\nvar EventSource = function(request, response, options) {\n  this.writable = true;\n  options = options || {};\n\n  this._stream = response.socket;\n  this._ping   = options.ping  || this.DEFAULT_PING;\n  this._retry  = options.retry || this.DEFAULT_RETRY;\n\n  var scheme       = driver.isSecureRequest(request) ? 'https:' : 'http:';\n  this.url         = scheme + '//' + request.headers.host + request.url;\n  this.lastEventId = request.headers['last-event-id'] || '';\n  this.readyState  = API.CONNECTING;\n\n  var headers = new Headers(),\n      self    = this;\n\n  if (options.headers) {\n    for (var key in options.headers) headers.set(key, options.headers[key]);\n  }\n\n  if (!this._stream || !this._stream.writable) return;\n  process.nextTick(function() { self._open() });\n\n  this._stream.setTimeout(0);\n  this._stream.setNoDelay(true);\n\n  var handshake = 'HTTP/1.1 200 OK\\r\\n' +\n                  'Content-Type: text/event-stream\\r\\n' +\n                  'Cache-Control: no-cache, no-store\\r\\n' +\n                  'Connection: close\\r\\n' +\n                  headers.toString() +\n                  '\\r\\n' +\n                  'retry: ' + Math.floor(this._retry * 1000) + '\\r\\n\\r\\n';\n\n  this._write(handshake);\n\n  this._stream.on('drain', function() { self.emit('drain') });\n\n  if (this._ping)\n    this._pingTimer = setInterval(function() { self.ping() }, this._ping * 1000);\n\n  ['error', 'end'].forEach(function(event) {\n    self._stream.on(event, function() { self.close() });\n  });\n};\nutil.inherits(EventSource, Stream);\n\nEventSource.isEventSource = function(request) {\n  if (request.method !== 'GET') return false;\n  var accept = (request.headers.accept || '').split(/\\s*,\\s*/);\n  return accept.indexOf('text/event-stream') >= 0;\n};\n\nvar instance = {\n  DEFAULT_PING:   10,\n  DEFAULT_RETRY:  5,\n\n  _write: function(chunk) {\n    if (!this.writable) return false;\n    try {\n      return this._stream.write(chunk, 'utf8');\n    } catch (e) {\n      return false;\n    }\n  },\n\n  _open: function() {\n    if (this.readyState !== API.CONNECTING) return;\n\n    this.readyState = API.OPEN;\n\n    var event = new Event('open');\n    event.initEvent('open', false, false);\n    this.dispatchEvent(event);\n  },\n\n  write: function(message) {\n    return this.send(message);\n  },\n\n  end: function(message) {\n    if (message !== undefined) this.write(message);\n    this.close();\n  },\n\n  send: function(message, options) {\n    if (this.readyState > API.OPEN) return false;\n\n    message = String(message).replace(/(\\r\\n|\\r|\\n)/g, '$1data: ');\n    options = options || {};\n\n    var frame = '';\n    if (options.event) frame += 'event: ' + options.event + '\\r\\n';\n    if (options.id)    frame += 'id: '    + options.id    + '\\r\\n';\n    frame += 'data: ' + message + '\\r\\n\\r\\n';\n\n    return this._write(frame);\n  },\n\n  ping: function() {\n    return this._write(':\\r\\n\\r\\n');\n  },\n\n  close: function() {\n    if (this.readyState > API.OPEN) return false;\n\n    this.readyState = API.CLOSED;\n    this.writable = false;\n    if (this._pingTimer) clearInterval(this._pingTimer);\n    if (this._stream) this._stream.end();\n\n    var event = new Event('close');\n    event.initEvent('close', false, false);\n    this.dispatchEvent(event);\n\n    return true;\n  }\n};\n\nfor (var method in instance) EventSource.prototype[method] = instance[method];\nfor (var key in EventTarget) EventSource.prototype[key] = EventTarget[key];\n\nmodule.exports = EventSource;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmF5ZS13ZWJzb2NrZXQvbGliL2ZheWUvZXZlbnRzb3VyY2UuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsa0JBQWtCLG9EQUF3QjtBQUMxQyxrQkFBa0IsbUJBQU8sQ0FBQyxrQkFBTTtBQUNoQyxrQkFBa0IsbUJBQU8sQ0FBQyx1RkFBa0I7QUFDNUMsa0JBQWtCLG1CQUFPLENBQUMsNEhBQStDO0FBQ3pFLGtCQUFrQixtQkFBTyxDQUFDLHNGQUFpQjtBQUMzQyxrQkFBa0IsbUJBQU8sQ0FBQyxnSEFBOEI7QUFDeEQsa0JBQWtCLG1CQUFPLENBQUMsa0dBQXVCOztBQUVqRDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsZ0NBQWdDLGNBQWM7O0FBRTlDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsd0NBQXdDLG9CQUFvQjs7QUFFNUQ7QUFDQSwrQ0FBK0MsYUFBYTs7QUFFNUQ7QUFDQSx3Q0FBd0MsY0FBYztBQUN0RCxHQUFHO0FBQ0g7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVoaXgvLi9ub2RlX21vZHVsZXMvZmF5ZS13ZWJzb2NrZXQvbGliL2ZheWUvZXZlbnRzb3VyY2UuanM/NjI5YSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciBTdHJlYW0gICAgICA9IHJlcXVpcmUoJ3N0cmVhbScpLlN0cmVhbSxcbiAgICB1dGlsICAgICAgICA9IHJlcXVpcmUoJ3V0aWwnKSxcbiAgICBkcml2ZXIgICAgICA9IHJlcXVpcmUoJ3dlYnNvY2tldC1kcml2ZXInKSxcbiAgICBIZWFkZXJzICAgICA9IHJlcXVpcmUoJ3dlYnNvY2tldC1kcml2ZXIvbGliL3dlYnNvY2tldC9kcml2ZXIvaGVhZGVycycpLFxuICAgIEFQSSAgICAgICAgID0gcmVxdWlyZSgnLi93ZWJzb2NrZXQvYXBpJyksXG4gICAgRXZlbnRUYXJnZXQgPSByZXF1aXJlKCcuL3dlYnNvY2tldC9hcGkvZXZlbnRfdGFyZ2V0JyksXG4gICAgRXZlbnQgICAgICAgPSByZXF1aXJlKCcuL3dlYnNvY2tldC9hcGkvZXZlbnQnKTtcblxudmFyIEV2ZW50U291cmNlID0gZnVuY3Rpb24ocmVxdWVzdCwgcmVzcG9uc2UsIG9wdGlvbnMpIHtcbiAgdGhpcy53cml0YWJsZSA9IHRydWU7XG4gIG9wdGlvbnMgPSBvcHRpb25zIHx8IHt9O1xuXG4gIHRoaXMuX3N0cmVhbSA9IHJlc3BvbnNlLnNvY2tldDtcbiAgdGhpcy5fcGluZyAgID0gb3B0aW9ucy5waW5nICB8fCB0aGlzLkRFRkFVTFRfUElORztcbiAgdGhpcy5fcmV0cnkgID0gb3B0aW9ucy5yZXRyeSB8fCB0aGlzLkRFRkFVTFRfUkVUUlk7XG5cbiAgdmFyIHNjaGVtZSAgICAgICA9IGRyaXZlci5pc1NlY3VyZVJlcXVlc3QocmVxdWVzdCkgPyAnaHR0cHM6JyA6ICdodHRwOic7XG4gIHRoaXMudXJsICAgICAgICAgPSBzY2hlbWUgKyAnLy8nICsgcmVxdWVzdC5oZWFkZXJzLmhvc3QgKyByZXF1ZXN0LnVybDtcbiAgdGhpcy5sYXN0RXZlbnRJZCA9IHJlcXVlc3QuaGVhZGVyc1snbGFzdC1ldmVudC1pZCddIHx8ICcnO1xuICB0aGlzLnJlYWR5U3RhdGUgID0gQVBJLkNPTk5FQ1RJTkc7XG5cbiAgdmFyIGhlYWRlcnMgPSBuZXcgSGVhZGVycygpLFxuICAgICAgc2VsZiAgICA9IHRoaXM7XG5cbiAgaWYgKG9wdGlvbnMuaGVhZGVycykge1xuICAgIGZvciAodmFyIGtleSBpbiBvcHRpb25zLmhlYWRlcnMpIGhlYWRlcnMuc2V0KGtleSwgb3B0aW9ucy5oZWFkZXJzW2tleV0pO1xuICB9XG5cbiAgaWYgKCF0aGlzLl9zdHJlYW0gfHwgIXRoaXMuX3N0cmVhbS53cml0YWJsZSkgcmV0dXJuO1xuICBwcm9jZXNzLm5leHRUaWNrKGZ1bmN0aW9uKCkgeyBzZWxmLl9vcGVuKCkgfSk7XG5cbiAgdGhpcy5fc3RyZWFtLnNldFRpbWVvdXQoMCk7XG4gIHRoaXMuX3N0cmVhbS5zZXROb0RlbGF5KHRydWUpO1xuXG4gIHZhciBoYW5kc2hha2UgPSAnSFRUUC8xLjEgMjAwIE9LXFxyXFxuJyArXG4gICAgICAgICAgICAgICAgICAnQ29udGVudC1UeXBlOiB0ZXh0L2V2ZW50LXN0cmVhbVxcclxcbicgK1xuICAgICAgICAgICAgICAgICAgJ0NhY2hlLUNvbnRyb2w6IG5vLWNhY2hlLCBuby1zdG9yZVxcclxcbicgK1xuICAgICAgICAgICAgICAgICAgJ0Nvbm5lY3Rpb246IGNsb3NlXFxyXFxuJyArXG4gICAgICAgICAgICAgICAgICBoZWFkZXJzLnRvU3RyaW5nKCkgK1xuICAgICAgICAgICAgICAgICAgJ1xcclxcbicgK1xuICAgICAgICAgICAgICAgICAgJ3JldHJ5OiAnICsgTWF0aC5mbG9vcih0aGlzLl9yZXRyeSAqIDEwMDApICsgJ1xcclxcblxcclxcbic7XG5cbiAgdGhpcy5fd3JpdGUoaGFuZHNoYWtlKTtcblxuICB0aGlzLl9zdHJlYW0ub24oJ2RyYWluJywgZnVuY3Rpb24oKSB7IHNlbGYuZW1pdCgnZHJhaW4nKSB9KTtcblxuICBpZiAodGhpcy5fcGluZylcbiAgICB0aGlzLl9waW5nVGltZXIgPSBzZXRJbnRlcnZhbChmdW5jdGlvbigpIHsgc2VsZi5waW5nKCkgfSwgdGhpcy5fcGluZyAqIDEwMDApO1xuXG4gIFsnZXJyb3InLCAnZW5kJ10uZm9yRWFjaChmdW5jdGlvbihldmVudCkge1xuICAgIHNlbGYuX3N0cmVhbS5vbihldmVudCwgZnVuY3Rpb24oKSB7IHNlbGYuY2xvc2UoKSB9KTtcbiAgfSk7XG59O1xudXRpbC5pbmhlcml0cyhFdmVudFNvdXJjZSwgU3RyZWFtKTtcblxuRXZlbnRTb3VyY2UuaXNFdmVudFNvdXJjZSA9IGZ1bmN0aW9uKHJlcXVlc3QpIHtcbiAgaWYgKHJlcXVlc3QubWV0aG9kICE9PSAnR0VUJykgcmV0dXJuIGZhbHNlO1xuICB2YXIgYWNjZXB0ID0gKHJlcXVlc3QuaGVhZGVycy5hY2NlcHQgfHwgJycpLnNwbGl0KC9cXHMqLFxccyovKTtcbiAgcmV0dXJuIGFjY2VwdC5pbmRleE9mKCd0ZXh0L2V2ZW50LXN0cmVhbScpID49IDA7XG59O1xuXG52YXIgaW5zdGFuY2UgPSB7XG4gIERFRkFVTFRfUElORzogICAxMCxcbiAgREVGQVVMVF9SRVRSWTogIDUsXG5cbiAgX3dyaXRlOiBmdW5jdGlvbihjaHVuaykge1xuICAgIGlmICghdGhpcy53cml0YWJsZSkgcmV0dXJuIGZhbHNlO1xuICAgIHRyeSB7XG4gICAgICByZXR1cm4gdGhpcy5fc3RyZWFtLndyaXRlKGNodW5rLCAndXRmOCcpO1xuICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH0sXG5cbiAgX29wZW46IGZ1bmN0aW9uKCkge1xuICAgIGlmICh0aGlzLnJlYWR5U3RhdGUgIT09IEFQSS5DT05ORUNUSU5HKSByZXR1cm47XG5cbiAgICB0aGlzLnJlYWR5U3RhdGUgPSBBUEkuT1BFTjtcblxuICAgIHZhciBldmVudCA9IG5ldyBFdmVudCgnb3BlbicpO1xuICAgIGV2ZW50LmluaXRFdmVudCgnb3BlbicsIGZhbHNlLCBmYWxzZSk7XG4gICAgdGhpcy5kaXNwYXRjaEV2ZW50KGV2ZW50KTtcbiAgfSxcblxuICB3cml0ZTogZnVuY3Rpb24obWVzc2FnZSkge1xuICAgIHJldHVybiB0aGlzLnNlbmQobWVzc2FnZSk7XG4gIH0sXG5cbiAgZW5kOiBmdW5jdGlvbihtZXNzYWdlKSB7XG4gICAgaWYgKG1lc3NhZ2UgIT09IHVuZGVmaW5lZCkgdGhpcy53cml0ZShtZXNzYWdlKTtcbiAgICB0aGlzLmNsb3NlKCk7XG4gIH0sXG5cbiAgc2VuZDogZnVuY3Rpb24obWVzc2FnZSwgb3B0aW9ucykge1xuICAgIGlmICh0aGlzLnJlYWR5U3RhdGUgPiBBUEkuT1BFTikgcmV0dXJuIGZhbHNlO1xuXG4gICAgbWVzc2FnZSA9IFN0cmluZyhtZXNzYWdlKS5yZXBsYWNlKC8oXFxyXFxufFxccnxcXG4pL2csICckMWRhdGE6ICcpO1xuICAgIG9wdGlvbnMgPSBvcHRpb25zIHx8IHt9O1xuXG4gICAgdmFyIGZyYW1lID0gJyc7XG4gICAgaWYgKG9wdGlvbnMuZXZlbnQpIGZyYW1lICs9ICdldmVudDogJyArIG9wdGlvbnMuZXZlbnQgKyAnXFxyXFxuJztcbiAgICBpZiAob3B0aW9ucy5pZCkgICAgZnJhbWUgKz0gJ2lkOiAnICAgICsgb3B0aW9ucy5pZCAgICArICdcXHJcXG4nO1xuICAgIGZyYW1lICs9ICdkYXRhOiAnICsgbWVzc2FnZSArICdcXHJcXG5cXHJcXG4nO1xuXG4gICAgcmV0dXJuIHRoaXMuX3dyaXRlKGZyYW1lKTtcbiAgfSxcblxuICBwaW5nOiBmdW5jdGlvbigpIHtcbiAgICByZXR1cm4gdGhpcy5fd3JpdGUoJzpcXHJcXG5cXHJcXG4nKTtcbiAgfSxcblxuICBjbG9zZTogZnVuY3Rpb24oKSB7XG4gICAgaWYgKHRoaXMucmVhZHlTdGF0ZSA+IEFQSS5PUEVOKSByZXR1cm4gZmFsc2U7XG5cbiAgICB0aGlzLnJlYWR5U3RhdGUgPSBBUEkuQ0xPU0VEO1xuICAgIHRoaXMud3JpdGFibGUgPSBmYWxzZTtcbiAgICBpZiAodGhpcy5fcGluZ1RpbWVyKSBjbGVhckludGVydmFsKHRoaXMuX3BpbmdUaW1lcik7XG4gICAgaWYgKHRoaXMuX3N0cmVhbSkgdGhpcy5fc3RyZWFtLmVuZCgpO1xuXG4gICAgdmFyIGV2ZW50ID0gbmV3IEV2ZW50KCdjbG9zZScpO1xuICAgIGV2ZW50LmluaXRFdmVudCgnY2xvc2UnLCBmYWxzZSwgZmFsc2UpO1xuICAgIHRoaXMuZGlzcGF0Y2hFdmVudChldmVudCk7XG5cbiAgICByZXR1cm4gdHJ1ZTtcbiAgfVxufTtcblxuZm9yICh2YXIgbWV0aG9kIGluIGluc3RhbmNlKSBFdmVudFNvdXJjZS5wcm90b3R5cGVbbWV0aG9kXSA9IGluc3RhbmNlW21ldGhvZF07XG5mb3IgKHZhciBrZXkgaW4gRXZlbnRUYXJnZXQpIEV2ZW50U291cmNlLnByb3RvdHlwZVtrZXldID0gRXZlbnRUYXJnZXRba2V5XTtcblxubW9kdWxlLmV4cG9ydHMgPSBFdmVudFNvdXJjZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/faye-websocket/lib/faye/eventsource.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/faye-websocket/lib/faye/websocket.js":
/*!***********************************************************!*\
  !*** ./node_modules/faye-websocket/lib/faye/websocket.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// API references:\n//\n// * https://html.spec.whatwg.org/multipage/comms.html#network\n// * https://dom.spec.whatwg.org/#interface-eventtarget\n// * https://dom.spec.whatwg.org/#interface-event\n\n\n\nvar util   = __webpack_require__(/*! util */ \"util\"),\n    driver = __webpack_require__(/*! websocket-driver */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver.js\"),\n    API    = __webpack_require__(/*! ./websocket/api */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api.js\");\n\nvar WebSocket = function(request, socket, body, protocols, options) {\n  options = options || {};\n\n  this._stream = socket;\n  this._driver = driver.http(request, { maxLength: options.maxLength, protocols: protocols });\n\n  var self = this;\n  if (!this._stream || !this._stream.writable) return;\n  if (!this._stream.readable) return this._stream.end();\n\n  var catchup = function() { self._stream.removeListener('data', catchup) };\n  this._stream.on('data', catchup);\n\n  API.call(this, options);\n\n  process.nextTick(function() {\n    self._driver.start();\n    self._driver.io.write(body);\n  });\n};\nutil.inherits(WebSocket, API);\n\nWebSocket.isWebSocket = function(request) {\n  return driver.isWebSocket(request);\n};\n\nWebSocket.validateOptions = function(options, validKeys) {\n  driver.validateOptions(options, validKeys);\n};\n\nWebSocket.WebSocket   = WebSocket;\nWebSocket.Client      = __webpack_require__(/*! ./websocket/client */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/client.js\");\nWebSocket.EventSource = __webpack_require__(/*! ./eventsource */ \"(ssr)/./node_modules/faye-websocket/lib/faye/eventsource.js\");\n\nmodule.exports        = WebSocket;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmF5ZS13ZWJzb2NrZXQvbGliL2ZheWUvd2Vic29ja2V0LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWE7O0FBRWIsYUFBYSxtQkFBTyxDQUFDLGtCQUFNO0FBQzNCLGFBQWEsbUJBQU8sQ0FBQyx1RkFBa0I7QUFDdkMsYUFBYSxtQkFBTyxDQUFDLHNGQUFpQjs7QUFFdEM7QUFDQTs7QUFFQTtBQUNBLHdDQUF3QyxvREFBb0Q7O0FBRTVGO0FBQ0E7QUFDQTs7QUFFQSw2QkFBNkI7QUFDN0I7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLHdCQUF3QixtQkFBTyxDQUFDLDRGQUFvQjtBQUNwRCx3QkFBd0IsbUJBQU8sQ0FBQyxrRkFBZTs7QUFFL0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZWhpeC8uL25vZGVfbW9kdWxlcy9mYXllLXdlYnNvY2tldC9saWIvZmF5ZS93ZWJzb2NrZXQuanM/Y2Q4OSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBBUEkgcmVmZXJlbmNlczpcbi8vXG4vLyAqIGh0dHBzOi8vaHRtbC5zcGVjLndoYXR3Zy5vcmcvbXVsdGlwYWdlL2NvbW1zLmh0bWwjbmV0d29ya1xuLy8gKiBodHRwczovL2RvbS5zcGVjLndoYXR3Zy5vcmcvI2ludGVyZmFjZS1ldmVudHRhcmdldFxuLy8gKiBodHRwczovL2RvbS5zcGVjLndoYXR3Zy5vcmcvI2ludGVyZmFjZS1ldmVudFxuXG4ndXNlIHN0cmljdCc7XG5cbnZhciB1dGlsICAgPSByZXF1aXJlKCd1dGlsJyksXG4gICAgZHJpdmVyID0gcmVxdWlyZSgnd2Vic29ja2V0LWRyaXZlcicpLFxuICAgIEFQSSAgICA9IHJlcXVpcmUoJy4vd2Vic29ja2V0L2FwaScpO1xuXG52YXIgV2ViU29ja2V0ID0gZnVuY3Rpb24ocmVxdWVzdCwgc29ja2V0LCBib2R5LCBwcm90b2NvbHMsIG9wdGlvbnMpIHtcbiAgb3B0aW9ucyA9IG9wdGlvbnMgfHwge307XG5cbiAgdGhpcy5fc3RyZWFtID0gc29ja2V0O1xuICB0aGlzLl9kcml2ZXIgPSBkcml2ZXIuaHR0cChyZXF1ZXN0LCB7IG1heExlbmd0aDogb3B0aW9ucy5tYXhMZW5ndGgsIHByb3RvY29sczogcHJvdG9jb2xzIH0pO1xuXG4gIHZhciBzZWxmID0gdGhpcztcbiAgaWYgKCF0aGlzLl9zdHJlYW0gfHwgIXRoaXMuX3N0cmVhbS53cml0YWJsZSkgcmV0dXJuO1xuICBpZiAoIXRoaXMuX3N0cmVhbS5yZWFkYWJsZSkgcmV0dXJuIHRoaXMuX3N0cmVhbS5lbmQoKTtcblxuICB2YXIgY2F0Y2h1cCA9IGZ1bmN0aW9uKCkgeyBzZWxmLl9zdHJlYW0ucmVtb3ZlTGlzdGVuZXIoJ2RhdGEnLCBjYXRjaHVwKSB9O1xuICB0aGlzLl9zdHJlYW0ub24oJ2RhdGEnLCBjYXRjaHVwKTtcblxuICBBUEkuY2FsbCh0aGlzLCBvcHRpb25zKTtcblxuICBwcm9jZXNzLm5leHRUaWNrKGZ1bmN0aW9uKCkge1xuICAgIHNlbGYuX2RyaXZlci5zdGFydCgpO1xuICAgIHNlbGYuX2RyaXZlci5pby53cml0ZShib2R5KTtcbiAgfSk7XG59O1xudXRpbC5pbmhlcml0cyhXZWJTb2NrZXQsIEFQSSk7XG5cbldlYlNvY2tldC5pc1dlYlNvY2tldCA9IGZ1bmN0aW9uKHJlcXVlc3QpIHtcbiAgcmV0dXJuIGRyaXZlci5pc1dlYlNvY2tldChyZXF1ZXN0KTtcbn07XG5cbldlYlNvY2tldC52YWxpZGF0ZU9wdGlvbnMgPSBmdW5jdGlvbihvcHRpb25zLCB2YWxpZEtleXMpIHtcbiAgZHJpdmVyLnZhbGlkYXRlT3B0aW9ucyhvcHRpb25zLCB2YWxpZEtleXMpO1xufTtcblxuV2ViU29ja2V0LldlYlNvY2tldCAgID0gV2ViU29ja2V0O1xuV2ViU29ja2V0LkNsaWVudCAgICAgID0gcmVxdWlyZSgnLi93ZWJzb2NrZXQvY2xpZW50Jyk7XG5XZWJTb2NrZXQuRXZlbnRTb3VyY2UgPSByZXF1aXJlKCcuL2V2ZW50c291cmNlJyk7XG5cbm1vZHVsZS5leHBvcnRzICAgICAgICA9IFdlYlNvY2tldDtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/faye-websocket/lib/faye/websocket.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api.js":
/*!***************************************************************!*\
  !*** ./node_modules/faye-websocket/lib/faye/websocket/api.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Stream      = (__webpack_require__(/*! stream */ \"stream\").Stream),\n    util        = __webpack_require__(/*! util */ \"util\"),\n    driver      = __webpack_require__(/*! websocket-driver */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver.js\"),\n    EventTarget = __webpack_require__(/*! ./api/event_target */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event_target.js\"),\n    Event       = __webpack_require__(/*! ./api/event */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event.js\");\n\nvar API = function(options) {\n  options = options || {};\n  driver.validateOptions(options, ['headers', 'extensions', 'maxLength', 'ping', 'proxy', 'tls', 'ca']);\n\n  this.readable = this.writable = true;\n\n  var headers = options.headers;\n  if (headers) {\n    for (var name in headers) this._driver.setHeader(name, headers[name]);\n  }\n\n  var extensions = options.extensions;\n  if (extensions) {\n    [].concat(extensions).forEach(this._driver.addExtension, this._driver);\n  }\n\n  this._ping          = options.ping;\n  this._pingId        = 0;\n  this.readyState     = API.CONNECTING;\n  this.bufferedAmount = 0;\n  this.protocol       = '';\n  this.url            = this._driver.url;\n  this.version        = this._driver.version;\n\n  var self = this;\n\n  this._driver.on('open',    function(e) { self._open() });\n  this._driver.on('message', function(e) { self._receiveMessage(e.data) });\n  this._driver.on('close',   function(e) { self._beginClose(e.reason, e.code) });\n\n  this._driver.on('error', function(error) {\n    self._emitError(error.message);\n  });\n  this.on('error', function() {});\n\n  this._driver.messages.on('drain', function() {\n    self.emit('drain');\n  });\n\n  if (this._ping)\n    this._pingTimer = setInterval(function() {\n      self._pingId += 1;\n      self.ping(self._pingId.toString());\n    }, this._ping * 1000);\n\n  this._configureStream();\n\n  if (!this._proxy) {\n    this._stream.pipe(this._driver.io);\n    this._driver.io.pipe(this._stream);\n  }\n};\nutil.inherits(API, Stream);\n\nAPI.CONNECTING = 0;\nAPI.OPEN       = 1;\nAPI.CLOSING    = 2;\nAPI.CLOSED     = 3;\n\nAPI.CLOSE_TIMEOUT = 30000;\n\nvar instance = {\n  write: function(data) {\n    return this.send(data);\n  },\n\n  end: function(data) {\n    if (data !== undefined) this.send(data);\n    this.close();\n  },\n\n  pause: function() {\n    return this._driver.messages.pause();\n  },\n\n  resume: function() {\n    return this._driver.messages.resume();\n  },\n\n  send: function(data) {\n    if (this.readyState > API.OPEN) return false;\n    if (!(data instanceof Buffer)) data = String(data);\n    return this._driver.messages.write(data);\n  },\n\n  ping: function(message, callback) {\n    if (this.readyState > API.OPEN) return false;\n    return this._driver.ping(message, callback);\n  },\n\n  close: function(code, reason) {\n    if (code === undefined) code = 1000;\n    if (reason === undefined) reason = '';\n\n    if (code !== 1000 && (code < 3000 || code > 4999))\n      throw new Error(\"Failed to execute 'close' on WebSocket: \" +\n                      \"The code must be either 1000, or between 3000 and 4999. \" +\n                      code + \" is neither.\");\n\n    if (this.readyState < API.CLOSING) {\n      var self = this;\n      this._closeTimer = setTimeout(function() {\n        self._beginClose('', 1006);\n      }, API.CLOSE_TIMEOUT);\n    }\n\n    if (this.readyState !== API.CLOSED) this.readyState = API.CLOSING;\n\n    this._driver.close(reason, code);\n  },\n\n  _configureStream: function() {\n    var self = this;\n\n    this._stream.setTimeout(0);\n    this._stream.setNoDelay(true);\n\n    ['close', 'end'].forEach(function(event) {\n      this._stream.on(event, function() { self._finalizeClose() });\n    }, this);\n\n    this._stream.on('error', function(error) {\n      self._emitError('Network error: ' + self.url + ': ' + error.message);\n      self._finalizeClose();\n    });\n  },\n\n  _open: function() {\n    if (this.readyState !== API.CONNECTING) return;\n\n    this.readyState = API.OPEN;\n    this.protocol = this._driver.protocol || '';\n\n    var event = new Event('open');\n    event.initEvent('open', false, false);\n    this.dispatchEvent(event);\n  },\n\n  _receiveMessage: function(data) {\n    if (this.readyState > API.OPEN) return false;\n\n    if (this.readable) this.emit('data', data);\n\n    var event = new Event('message', { data: data });\n    event.initEvent('message', false, false);\n    this.dispatchEvent(event);\n  },\n\n  _emitError: function(message) {\n    if (this.readyState >= API.CLOSING) return;\n\n    var event = new Event('error', { message: message });\n    event.initEvent('error', false, false);\n    this.dispatchEvent(event);\n  },\n\n  _beginClose: function(reason, code) {\n    if (this.readyState === API.CLOSED) return;\n    this.readyState = API.CLOSING;\n    this._closeParams = [reason, code];\n\n    if (this._stream) {\n      this._stream.destroy();\n      if (!this._stream.readable) this._finalizeClose();\n    }\n  },\n\n  _finalizeClose: function() {\n    if (this.readyState === API.CLOSED) return;\n    this.readyState = API.CLOSED;\n\n    if (this._closeTimer) clearTimeout(this._closeTimer);\n    if (this._pingTimer) clearInterval(this._pingTimer);\n    if (this._stream) this._stream.end();\n\n    if (this.readable) this.emit('end');\n    this.readable = this.writable = false;\n\n    var reason = this._closeParams ? this._closeParams[0] : '',\n        code   = this._closeParams ? this._closeParams[1] : 1006;\n\n    var event = new Event('close', { code: code, reason: reason });\n    event.initEvent('close', false, false);\n    this.dispatchEvent(event);\n  }\n};\n\nfor (var method in instance) API.prototype[method] = instance[method];\nfor (var key in EventTarget) API.prototype[key] = EventTarget[key];\n\nmodule.exports = API;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event.js":
/*!*********************************************************************!*\
  !*** ./node_modules/faye-websocket/lib/faye/websocket/api/event.js ***!
  \*********************************************************************/
/***/ ((module) => {

eval("\n\nvar Event = function(eventType, options) {\n  this.type = eventType;\n  for (var key in options)\n    this[key] = options[key];\n};\n\nEvent.prototype.initEvent = function(eventType, canBubble, cancelable) {\n  this.type       = eventType;\n  this.bubbles    = canBubble;\n  this.cancelable = cancelable;\n};\n\nEvent.prototype.stopPropagation = function() {};\nEvent.prototype.preventDefault  = function() {};\n\nEvent.CAPTURING_PHASE = 1;\nEvent.AT_TARGET       = 2;\nEvent.BUBBLING_PHASE  = 3;\n\nmodule.exports = Event;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmF5ZS13ZWJzb2NrZXQvbGliL2ZheWUvd2Vic29ja2V0L2FwaS9ldmVudC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZWhpeC8uL25vZGVfbW9kdWxlcy9mYXllLXdlYnNvY2tldC9saWIvZmF5ZS93ZWJzb2NrZXQvYXBpL2V2ZW50LmpzP2ExZTQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgRXZlbnQgPSBmdW5jdGlvbihldmVudFR5cGUsIG9wdGlvbnMpIHtcbiAgdGhpcy50eXBlID0gZXZlbnRUeXBlO1xuICBmb3IgKHZhciBrZXkgaW4gb3B0aW9ucylcbiAgICB0aGlzW2tleV0gPSBvcHRpb25zW2tleV07XG59O1xuXG5FdmVudC5wcm90b3R5cGUuaW5pdEV2ZW50ID0gZnVuY3Rpb24oZXZlbnRUeXBlLCBjYW5CdWJibGUsIGNhbmNlbGFibGUpIHtcbiAgdGhpcy50eXBlICAgICAgID0gZXZlbnRUeXBlO1xuICB0aGlzLmJ1YmJsZXMgICAgPSBjYW5CdWJibGU7XG4gIHRoaXMuY2FuY2VsYWJsZSA9IGNhbmNlbGFibGU7XG59O1xuXG5FdmVudC5wcm90b3R5cGUuc3RvcFByb3BhZ2F0aW9uID0gZnVuY3Rpb24oKSB7fTtcbkV2ZW50LnByb3RvdHlwZS5wcmV2ZW50RGVmYXVsdCAgPSBmdW5jdGlvbigpIHt9O1xuXG5FdmVudC5DQVBUVVJJTkdfUEhBU0UgPSAxO1xuRXZlbnQuQVRfVEFSR0VUICAgICAgID0gMjtcbkV2ZW50LkJVQkJMSU5HX1BIQVNFICA9IDM7XG5cbm1vZHVsZS5leHBvcnRzID0gRXZlbnQ7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event_target.js":
/*!****************************************************************************!*\
  !*** ./node_modules/faye-websocket/lib/faye/websocket/api/event_target.js ***!
  \****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Event = __webpack_require__(/*! ./event */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event.js\");\n\nvar EventTarget = {\n  onopen:     null,\n  onmessage:  null,\n  onerror:    null,\n  onclose:    null,\n\n  addEventListener: function(eventType, listener, useCapture) {\n    this.on(eventType, listener);\n  },\n\n  removeEventListener: function(eventType, listener, useCapture) {\n    this.removeListener(eventType, listener);\n  },\n\n  dispatchEvent: function(event) {\n    event.target = event.currentTarget = this;\n    event.eventPhase = Event.AT_TARGET;\n\n    if (this['on' + event.type])\n      this['on' + event.type](event);\n\n    this.emit(event.type, event);\n  }\n};\n\nmodule.exports = EventTarget;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmF5ZS13ZWJzb2NrZXQvbGliL2ZheWUvd2Vic29ja2V0L2FwaS9ldmVudF90YXJnZXQuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsWUFBWSxtQkFBTyxDQUFDLG9GQUFTOztBQUU3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL2RlaGl4Ly4vbm9kZV9tb2R1bGVzL2ZheWUtd2Vic29ja2V0L2xpYi9mYXllL3dlYnNvY2tldC9hcGkvZXZlbnRfdGFyZ2V0LmpzP2I2MjciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgRXZlbnQgPSByZXF1aXJlKCcuL2V2ZW50Jyk7XG5cbnZhciBFdmVudFRhcmdldCA9IHtcbiAgb25vcGVuOiAgICAgbnVsbCxcbiAgb25tZXNzYWdlOiAgbnVsbCxcbiAgb25lcnJvcjogICAgbnVsbCxcbiAgb25jbG9zZTogICAgbnVsbCxcblxuICBhZGRFdmVudExpc3RlbmVyOiBmdW5jdGlvbihldmVudFR5cGUsIGxpc3RlbmVyLCB1c2VDYXB0dXJlKSB7XG4gICAgdGhpcy5vbihldmVudFR5cGUsIGxpc3RlbmVyKTtcbiAgfSxcblxuICByZW1vdmVFdmVudExpc3RlbmVyOiBmdW5jdGlvbihldmVudFR5cGUsIGxpc3RlbmVyLCB1c2VDYXB0dXJlKSB7XG4gICAgdGhpcy5yZW1vdmVMaXN0ZW5lcihldmVudFR5cGUsIGxpc3RlbmVyKTtcbiAgfSxcblxuICBkaXNwYXRjaEV2ZW50OiBmdW5jdGlvbihldmVudCkge1xuICAgIGV2ZW50LnRhcmdldCA9IGV2ZW50LmN1cnJlbnRUYXJnZXQgPSB0aGlzO1xuICAgIGV2ZW50LmV2ZW50UGhhc2UgPSBFdmVudC5BVF9UQVJHRVQ7XG5cbiAgICBpZiAodGhpc1snb24nICsgZXZlbnQudHlwZV0pXG4gICAgICB0aGlzWydvbicgKyBldmVudC50eXBlXShldmVudCk7XG5cbiAgICB0aGlzLmVtaXQoZXZlbnQudHlwZSwgZXZlbnQpO1xuICB9XG59O1xuXG5tb2R1bGUuZXhwb3J0cyA9IEV2ZW50VGFyZ2V0O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event_target.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/faye-websocket/lib/faye/websocket/client.js":
/*!******************************************************************!*\
  !*** ./node_modules/faye-websocket/lib/faye/websocket/client.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar util   = __webpack_require__(/*! util */ \"util\"),\n    net    = __webpack_require__(/*! net */ \"net\"),\n    tls    = __webpack_require__(/*! tls */ \"tls\"),\n    url    = __webpack_require__(/*! url */ \"url\"),\n    driver = __webpack_require__(/*! websocket-driver */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver.js\"),\n    API    = __webpack_require__(/*! ./api */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api.js\"),\n    Event  = __webpack_require__(/*! ./api/event */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event.js\");\n\nvar DEFAULT_PORTS    = { 'http:': 80, 'https:': 443, 'ws:':80, 'wss:': 443 },\n    SECURE_PROTOCOLS = ['https:', 'wss:'];\n\nvar Client = function(_url, protocols, options) {\n  options = options || {};\n\n  this.url     = _url;\n  this._driver = driver.client(this.url, { maxLength: options.maxLength, protocols: protocols });\n\n  ['open', 'error'].forEach(function(event) {\n    this._driver.on(event, function() {\n      self.headers    = self._driver.headers;\n      self.statusCode = self._driver.statusCode;\n    });\n  }, this);\n\n  var proxy      = options.proxy || {},\n      endpoint   = url.parse(proxy.origin || this.url),\n      port       = endpoint.port || DEFAULT_PORTS[endpoint.protocol],\n      secure     = SECURE_PROTOCOLS.indexOf(endpoint.protocol) >= 0,\n      onConnect  = function() { self._onConnect() },\n      netOptions = options.net || {},\n      originTLS  = options.tls || {},\n      socketTLS  = proxy.origin ? (proxy.tls || {}) : originTLS,\n      self       = this;\n\n  netOptions.host = socketTLS.host = endpoint.hostname;\n  netOptions.port = socketTLS.port = port;\n\n  originTLS.ca = originTLS.ca || options.ca;\n  socketTLS.servername = socketTLS.servername || endpoint.hostname;\n\n  this._stream = secure\n               ? tls.connect(socketTLS, onConnect)\n               : net.connect(netOptions, onConnect);\n\n  if (proxy.origin) this._configureProxy(proxy, originTLS);\n\n  API.call(this, options);\n};\nutil.inherits(Client, API);\n\nClient.prototype._onConnect = function() {\n  var worker = this._proxy || this._driver;\n  worker.start();\n};\n\nClient.prototype._configureProxy = function(proxy, originTLS) {\n  var uri    = url.parse(this.url),\n      secure = SECURE_PROTOCOLS.indexOf(uri.protocol) >= 0,\n      self   = this,\n      name;\n\n  this._proxy = this._driver.proxy(proxy.origin);\n\n  if (proxy.headers) {\n    for (name in proxy.headers) this._proxy.setHeader(name, proxy.headers[name]);\n  }\n\n  this._proxy.pipe(this._stream, { end: false });\n  this._stream.pipe(this._proxy);\n\n  this._proxy.on('connect', function() {\n    if (secure) {\n      var options = { socket: self._stream, servername: uri.hostname };\n      for (name in originTLS) options[name] = originTLS[name];\n      self._stream = tls.connect(options);\n      self._configureStream();\n    }\n    self._driver.io.pipe(self._stream);\n    self._stream.pipe(self._driver.io);\n    self._driver.start();\n  });\n\n  this._proxy.on('error', function(error) {\n    self._driver.emit('error', error);\n  });\n};\n\nmodule.exports = Client;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/faye-websocket/lib/faye/websocket/client.js\n");

/***/ })

};
;