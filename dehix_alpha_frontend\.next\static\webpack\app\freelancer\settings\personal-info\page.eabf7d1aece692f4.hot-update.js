"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/settings/personal-info/page",{

/***/ "(app-pages-browser)/./src/components/fileUpload/coverLetter.tsx":
/*!***************************************************!*\
  !*** ./src/components/fileUpload/coverLetter.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FileText_UploadCloud_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,UploadCloud,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_UploadCloud_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,UploadCloud,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_UploadCloud_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,UploadCloud,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cloud-upload.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/axiosinstance */ \"(app-pages-browser)/./src/lib/axiosinstance.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst allowedCoverLetterFormats = [\n    \"application/pdf\",\n    \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\"\n];\nconst maxCoverLetterSize = 5 * 1024 * 1024; // 5MB\nconst CoverLetterUpload = ()=>{\n    _s();\n    const [selectedCoverLetter, setSelectedCoverLetter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedFileName, setUploadedFileName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [coverLetterPreviewURL, setCoverLetterPreviewURL] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const truncateFileName = (fileName)=>{\n        if (!fileName) return \"\"; // Handle undefined values\n        const maxLength = 20;\n        const extension = fileName.includes(\".\") ? fileName.substring(fileName.lastIndexOf(\".\")) : \"\"; // Handle files without extension\n        return fileName.length > maxLength ? \"\".concat(fileName.substring(0, maxLength - extension.length), \"...\").concat(extension) : fileName;\n    };\n    const handleCoverLetterChange = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) {\n            if (allowedCoverLetterFormats.includes(file.type)) {\n                if (file.size <= maxCoverLetterSize) {\n                    setSelectedCoverLetter(file);\n                    setUploadedFileName(file.name);\n                    // Create a preview URL only for PDFs\n                    if (file.type === \"application/pdf\") {\n                        const fileURL = URL.createObjectURL(file);\n                        setCoverLetterPreviewURL(fileURL);\n                    } else {\n                        setCoverLetterPreviewURL(null);\n                    }\n                } else {\n                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                        variant: \"destructive\",\n                        title: \"File too large\",\n                        description: \"Cover letter size should not exceed 5MB.\"\n                    });\n                }\n            } else {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                    variant: \"destructive\",\n                    title: \"Invalid file type\",\n                    description: \"Supported formats: PDF, DOCX.\"\n                });\n            }\n        }\n    };\n    const handleUploadClick = async (e)=>{\n        e.preventDefault();\n        if (!selectedCoverLetter) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                variant: \"destructive\",\n                title: \"No Cover Letter Selected\",\n                description: \"Please select a cover letter before uploading.\"\n            });\n            return;\n        }\n        const formData = new FormData();\n        formData.append(\"coverLetter\", selectedCoverLetter);\n        console.log(\"Uploading cover letter:\", {\n            fileName: selectedCoverLetter.name,\n            fileSize: selectedCoverLetter.size,\n            fileType: selectedCoverLetter.type\n        });\n        try {\n            setIsUploading(true);\n            const postResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_4__.axiosInstance.post(\"/register/upload-image\", formData, {\n                headers: {\n                    \"Content-Type\": \"multipart/form-data\"\n                }\n            });\n            const { Location } = postResponse.data.data;\n            if (!Location) throw new Error(\"Failed to upload the cover letter.\");\n            const putResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_4__.axiosInstance.put(\"/freelancer\", {\n                coverLetter: Location\n            });\n            if (putResponse.status === 200) {\n                setUploadedFileName(selectedCoverLetter.name);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                    title: \"Success\",\n                    description: \"Cover letter uploaded successfully!\"\n                });\n            } else {\n                throw new Error(\"Failed to update cover letter.\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response_data, _error_response2;\n            console.error(\"Cover letter upload error:\", error);\n            console.error(\"Error response:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            console.error(\"Error status:\", (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: ((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data = _error_response2.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Something went wrong. Please try again.\"\n            });\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchCoverLetter = async ()=>{\n            try {\n                const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_4__.axiosInstance.get(\"/freelancer\"); // API to get user profile\n                if (response.data.coverLetter) {\n                    setUploadedFileName(response.data.coverLetter); // Set the stored cover letter URL\n                    setCoverLetterPreviewURL(response.data.coverLetter); // Set the preview URL\n                }\n            } catch (error) {\n                console.error(\"Error fetching cover letter:\", error);\n            }\n        };\n        fetchCoverLetter();\n    }, []);\n    const handleCancelClick = ()=>{\n        setSelectedCoverLetter(null);\n        setCoverLetterPreviewURL(null);\n        setUploadedFileName(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"upload-form max-w-md mx-auto rounded shadow-md p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 flex flex-col items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center justify-center border-dashed border-2 border-gray-400 rounded-lg p-6 w-full cursor-pointer\",\n                    onClick: ()=>{\n                        var _fileInputRef_current;\n                        return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                    },\n                    children: selectedCoverLetter ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full flex flex-col items-center gap-4 text-gray-700 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-1 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"truncate\",\n                                        children: truncateFileName(selectedCoverLetter.name)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-red-600 text-white rounded-full p-1 hover:bg-red-700\",\n                                        onClick: handleCancelClick,\n                                        \"aria-label\": \"Remove file\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_UploadCloud_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 15\n                            }, undefined),\n                            coverLetterPreviewURL ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                src: coverLetterPreviewURL,\n                                title: \"Cover Letter Preview\",\n                                className: \"w-full h-40 border rounded\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 p-2 bg-gray-100 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_UploadCloud_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"text-gray-500 w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: truncateFileName(selectedCoverLetter.name)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_UploadCloud_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"text-gray-500 w-12 h-12 mb-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-700 text-center\",\n                                children: \"Drag and drop your cover letter here or click to upload\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-600 text-xs md:text-sm\",\n                                    children: \"Supported formats: PDF, DOCX.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"file\",\n                                accept: allowedCoverLetterFormats.join(\",\"),\n                                onChange: handleCoverLetterChange,\n                                className: \"hidden\",\n                                ref: fileInputRef\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, undefined),\n                selectedCoverLetter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: handleUploadClick,\n                    className: \"w-full\",\n                    disabled: isUploading,\n                    children: isUploading ? \"Uploading...\" : \"Upload Cover Letter\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 11\n                }, undefined),\n                uploadedFileName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-center text-gray-600\",\n                    children: [\n                        \"Uploaded:\",\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: truncateFileName(uploadedFileName || \"\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\coverLetter.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CoverLetterUpload, \"afZ8K3qnbGiuzZkktkTcYBxKsg4=\");\n_c = CoverLetterUpload;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CoverLetterUpload);\nvar _c;\n$RefreshReg$(_c, \"CoverLetterUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/fileUpload/coverLetter.tsx\n"));

/***/ })

});