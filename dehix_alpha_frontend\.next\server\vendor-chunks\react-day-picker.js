"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-day-picker";
exports.ids = ["vendor-chunks/react-day-picker"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-day-picker/dist/index.esm.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-day-picker/dist/index.esm.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   Caption: () => (/* binding */ Caption),\n/* harmony export */   CaptionDropdowns: () => (/* binding */ CaptionDropdowns),\n/* harmony export */   CaptionLabel: () => (/* binding */ CaptionLabel),\n/* harmony export */   CaptionNavigation: () => (/* binding */ CaptionNavigation),\n/* harmony export */   Day: () => (/* binding */ Day),\n/* harmony export */   DayContent: () => (/* binding */ DayContent),\n/* harmony export */   DayPicker: () => (/* binding */ DayPicker),\n/* harmony export */   DayPickerContext: () => (/* binding */ DayPickerContext),\n/* harmony export */   DayPickerProvider: () => (/* binding */ DayPickerProvider),\n/* harmony export */   Dropdown: () => (/* binding */ Dropdown),\n/* harmony export */   FocusContext: () => (/* binding */ FocusContext),\n/* harmony export */   FocusProvider: () => (/* binding */ FocusProvider),\n/* harmony export */   Footer: () => (/* binding */ Footer),\n/* harmony export */   Head: () => (/* binding */ Head),\n/* harmony export */   HeadRow: () => (/* binding */ HeadRow),\n/* harmony export */   IconDropdown: () => (/* binding */ IconDropdown),\n/* harmony export */   IconLeft: () => (/* binding */ IconLeft),\n/* harmony export */   IconRight: () => (/* binding */ IconRight),\n/* harmony export */   InternalModifier: () => (/* binding */ InternalModifier),\n/* harmony export */   Months: () => (/* binding */ Months),\n/* harmony export */   NavigationContext: () => (/* binding */ NavigationContext),\n/* harmony export */   NavigationProvider: () => (/* binding */ NavigationProvider),\n/* harmony export */   RootProvider: () => (/* binding */ RootProvider),\n/* harmony export */   Row: () => (/* binding */ Row),\n/* harmony export */   SelectMultipleContext: () => (/* binding */ SelectMultipleContext),\n/* harmony export */   SelectMultipleProvider: () => (/* binding */ SelectMultipleProvider),\n/* harmony export */   SelectMultipleProviderInternal: () => (/* binding */ SelectMultipleProviderInternal),\n/* harmony export */   SelectRangeContext: () => (/* binding */ SelectRangeContext),\n/* harmony export */   SelectRangeProvider: () => (/* binding */ SelectRangeProvider),\n/* harmony export */   SelectRangeProviderInternal: () => (/* binding */ SelectRangeProviderInternal),\n/* harmony export */   SelectSingleContext: () => (/* binding */ SelectSingleContext),\n/* harmony export */   SelectSingleProvider: () => (/* binding */ SelectSingleProvider),\n/* harmony export */   SelectSingleProviderInternal: () => (/* binding */ SelectSingleProviderInternal),\n/* harmony export */   WeekNumber: () => (/* binding */ WeekNumber),\n/* harmony export */   addToRange: () => (/* binding */ addToRange),\n/* harmony export */   isDateAfterType: () => (/* binding */ isDateAfterType),\n/* harmony export */   isDateBeforeType: () => (/* binding */ isDateBeforeType),\n/* harmony export */   isDateInterval: () => (/* binding */ isDateInterval),\n/* harmony export */   isDateRange: () => (/* binding */ isDateRange),\n/* harmony export */   isDayOfWeekType: () => (/* binding */ isDayOfWeekType),\n/* harmony export */   isDayPickerDefault: () => (/* binding */ isDayPickerDefault),\n/* harmony export */   isDayPickerMultiple: () => (/* binding */ isDayPickerMultiple),\n/* harmony export */   isDayPickerRange: () => (/* binding */ isDayPickerRange),\n/* harmony export */   isDayPickerSingle: () => (/* binding */ isDayPickerSingle),\n/* harmony export */   isMatch: () => (/* binding */ isMatch),\n/* harmony export */   useActiveModifiers: () => (/* binding */ useActiveModifiers),\n/* harmony export */   useDayPicker: () => (/* binding */ useDayPicker),\n/* harmony export */   useDayRender: () => (/* binding */ useDayRender),\n/* harmony export */   useFocusContext: () => (/* binding */ useFocusContext),\n/* harmony export */   useInput: () => (/* binding */ useInput),\n/* harmony export */   useNavigation: () => (/* binding */ useNavigation),\n/* harmony export */   useSelectMultiple: () => (/* binding */ useSelectMultiple),\n/* harmony export */   useSelectRange: () => (/* binding */ useSelectRange),\n/* harmony export */   useSelectSingle: () => (/* binding */ useSelectSingle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/format.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/startOfMonth.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/endOfMonth.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/startOfDay.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/isSameYear.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/setMonth.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/setYear.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/startOfYear.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/differenceInCalendarMonths.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/addMonths.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/isSameMonth.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/isBefore.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/startOfISOWeek.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/startOfWeek.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/addDays.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/isSameDay.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/isAfter.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/subDays.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/differenceInCalendarDays.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/isDate.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/addWeeks.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/addYears.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/endOfISOWeek.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/endOfWeek.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/max.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/min.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/getUnixTime.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/getISOWeek.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/getWeek.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/getWeeksInMonth.mjs\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/parse.mjs\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! date-fns/locale */ \"(ssr)/./node_modules/date-fns/locale/en-US.mjs\");\n\n\n\n\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nfunction __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\n/** Returns true when the props are of type {@link DayPickerMultipleProps}. */\nfunction isDayPickerMultiple(props) {\n    return props.mode === 'multiple';\n}\n\n/** Returns true when the props are of type {@link DayPickerRangeProps}. */\nfunction isDayPickerRange(props) {\n    return props.mode === 'range';\n}\n\n/** Returns true when the props are of type {@link DayPickerSingleProps}. */\nfunction isDayPickerSingle(props) {\n    return props.mode === 'single';\n}\n\n/**\n * The name of the default CSS classes.\n */\nvar defaultClassNames = {\n    root: 'rdp',\n    multiple_months: 'rdp-multiple_months',\n    with_weeknumber: 'rdp-with_weeknumber',\n    vhidden: 'rdp-vhidden',\n    button_reset: 'rdp-button_reset',\n    button: 'rdp-button',\n    caption: 'rdp-caption',\n    caption_start: 'rdp-caption_start',\n    caption_end: 'rdp-caption_end',\n    caption_between: 'rdp-caption_between',\n    caption_label: 'rdp-caption_label',\n    caption_dropdowns: 'rdp-caption_dropdowns',\n    dropdown: 'rdp-dropdown',\n    dropdown_month: 'rdp-dropdown_month',\n    dropdown_year: 'rdp-dropdown_year',\n    dropdown_icon: 'rdp-dropdown_icon',\n    months: 'rdp-months',\n    month: 'rdp-month',\n    table: 'rdp-table',\n    tbody: 'rdp-tbody',\n    tfoot: 'rdp-tfoot',\n    head: 'rdp-head',\n    head_row: 'rdp-head_row',\n    head_cell: 'rdp-head_cell',\n    nav: 'rdp-nav',\n    nav_button: 'rdp-nav_button',\n    nav_button_previous: 'rdp-nav_button_previous',\n    nav_button_next: 'rdp-nav_button_next',\n    nav_icon: 'rdp-nav_icon',\n    row: 'rdp-row',\n    weeknumber: 'rdp-weeknumber',\n    cell: 'rdp-cell',\n    day: 'rdp-day',\n    day_today: 'rdp-day_today',\n    day_outside: 'rdp-day_outside',\n    day_selected: 'rdp-day_selected',\n    day_disabled: 'rdp-day_disabled',\n    day_hidden: 'rdp-day_hidden',\n    day_range_start: 'rdp-day_range_start',\n    day_range_end: 'rdp-day_range_end',\n    day_range_middle: 'rdp-day_range_middle'\n};\n\n/**\n * The default formatter for the caption.\n */\nfunction formatCaption(month, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(month, 'LLLL y', options);\n}\n\n/**\n * The default formatter for the Day button.\n */\nfunction formatDay(day, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(day, 'd', options);\n}\n\n/**\n * The default formatter for the Month caption.\n */\nfunction formatMonthCaption(month, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(month, 'LLLL', options);\n}\n\n/**\n * The default formatter for the week number.\n */\nfunction formatWeekNumber(weekNumber) {\n    return \"\".concat(weekNumber);\n}\n\n/**\n * The default formatter for the name of the weekday.\n */\nfunction formatWeekdayName(weekday, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(weekday, 'cccccc', options);\n}\n\n/**\n * The default formatter for the Year caption.\n */\nfunction formatYearCaption(year, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(year, 'yyyy', options);\n}\n\nvar formatters = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    formatCaption: formatCaption,\n    formatDay: formatDay,\n    formatMonthCaption: formatMonthCaption,\n    formatWeekNumber: formatWeekNumber,\n    formatWeekdayName: formatWeekdayName,\n    formatYearCaption: formatYearCaption\n});\n\n/**\n * The default ARIA label for the day button.\n */\nvar labelDay = function (day, activeModifiers, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(day, 'do MMMM (EEEE)', options);\n};\n\n/**\n * The default ARIA label for the WeekNumber element.\n */\nvar labelMonthDropdown = function () {\n    return 'Month: ';\n};\n\n/**\n * The default ARIA label for next month button in navigation\n */\nvar labelNext = function () {\n    return 'Go to next month';\n};\n\n/**\n * The default ARIA label for previous month button in navigation\n */\nvar labelPrevious = function () {\n    return 'Go to previous month';\n};\n\n/**\n * The default ARIA label for the Weekday element.\n */\nvar labelWeekday = function (day, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(day, 'cccc', options);\n};\n\n/**\n * The default ARIA label for the WeekNumber element.\n */\nvar labelWeekNumber = function (n) {\n    return \"Week n. \".concat(n);\n};\n\n/**\n * The default ARIA label for the WeekNumber element.\n */\nvar labelYearDropdown = function () {\n    return 'Year: ';\n};\n\nvar labels = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    labelDay: labelDay,\n    labelMonthDropdown: labelMonthDropdown,\n    labelNext: labelNext,\n    labelPrevious: labelPrevious,\n    labelWeekNumber: labelWeekNumber,\n    labelWeekday: labelWeekday,\n    labelYearDropdown: labelYearDropdown\n});\n\n/**\n * Returns the default values to use in the DayPickerContext, in case they are\n * not passed down with the DayPicker initial props.\n */\nfunction getDefaultContextValues() {\n    var captionLayout = 'buttons';\n    var classNames = defaultClassNames;\n    var locale = date_fns_locale__WEBPACK_IMPORTED_MODULE_3__.enUS;\n    var modifiersClassNames = {};\n    var modifiers = {};\n    var numberOfMonths = 1;\n    var styles = {};\n    var today = new Date();\n    return {\n        captionLayout: captionLayout,\n        classNames: classNames,\n        formatters: formatters,\n        labels: labels,\n        locale: locale,\n        modifiersClassNames: modifiersClassNames,\n        modifiers: modifiers,\n        numberOfMonths: numberOfMonths,\n        styles: styles,\n        today: today,\n        mode: 'default'\n    };\n}\n\n/** Return the `fromDate` and `toDate` prop values values parsing the DayPicker props. */\nfunction parseFromToProps(props) {\n    var fromYear = props.fromYear, toYear = props.toYear, fromMonth = props.fromMonth, toMonth = props.toMonth;\n    var fromDate = props.fromDate, toDate = props.toDate;\n    if (fromMonth) {\n        fromDate = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(fromMonth);\n    }\n    else if (fromYear) {\n        fromDate = new Date(fromYear, 0, 1);\n    }\n    if (toMonth) {\n        toDate = (0,date_fns__WEBPACK_IMPORTED_MODULE_5__.endOfMonth)(toMonth);\n    }\n    else if (toYear) {\n        toDate = new Date(toYear, 11, 31);\n    }\n    return {\n        fromDate: fromDate ? (0,date_fns__WEBPACK_IMPORTED_MODULE_6__.startOfDay)(fromDate) : undefined,\n        toDate: toDate ? (0,date_fns__WEBPACK_IMPORTED_MODULE_6__.startOfDay)(toDate) : undefined\n    };\n}\n\n/**\n * The DayPicker context shares the props passed to DayPicker within internal\n * and custom components. It is used to set the default values and perform\n * one-time calculations required to render the days.\n *\n * Access to this context from the {@link useDayPicker} hook.\n */\nvar DayPickerContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/**\n * The provider for the {@link DayPickerContext}, assigning the defaults from the\n * initial DayPicker props.\n */\nfunction DayPickerProvider(props) {\n    var _a;\n    var initialProps = props.initialProps;\n    var defaultContextValues = getDefaultContextValues();\n    var _b = parseFromToProps(initialProps), fromDate = _b.fromDate, toDate = _b.toDate;\n    var captionLayout = (_a = initialProps.captionLayout) !== null && _a !== void 0 ? _a : defaultContextValues.captionLayout;\n    if (captionLayout !== 'buttons' && (!fromDate || !toDate)) {\n        // When no from/to dates are set, the caption is always buttons\n        captionLayout = 'buttons';\n    }\n    var onSelect;\n    if (isDayPickerSingle(initialProps) ||\n        isDayPickerMultiple(initialProps) ||\n        isDayPickerRange(initialProps)) {\n        onSelect = initialProps.onSelect;\n    }\n    var value = __assign(__assign(__assign({}, defaultContextValues), initialProps), { captionLayout: captionLayout, classNames: __assign(__assign({}, defaultContextValues.classNames), initialProps.classNames), components: __assign({}, initialProps.components), formatters: __assign(__assign({}, defaultContextValues.formatters), initialProps.formatters), fromDate: fromDate, labels: __assign(__assign({}, defaultContextValues.labels), initialProps.labels), mode: initialProps.mode || defaultContextValues.mode, modifiers: __assign(__assign({}, defaultContextValues.modifiers), initialProps.modifiers), modifiersClassNames: __assign(__assign({}, defaultContextValues.modifiersClassNames), initialProps.modifiersClassNames), onSelect: onSelect, styles: __assign(__assign({}, defaultContextValues.styles), initialProps.styles), toDate: toDate });\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DayPickerContext.Provider, { value: value, children: props.children }));\n}\n/**\n * Hook to access the {@link DayPickerContextValue}.\n *\n * Use the DayPicker context to access to the props passed to DayPicker inside\n * internal or custom components.\n */\nfunction useDayPicker() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(DayPickerContext);\n    if (!context) {\n        throw new Error(\"useDayPicker must be used within a DayPickerProvider.\");\n    }\n    return context;\n}\n\n/** Render the caption for the displayed month. This component is used when `captionLayout=\"buttons\"`. */\nfunction CaptionLabel(props) {\n    var _a = useDayPicker(), locale = _a.locale, classNames = _a.classNames, styles = _a.styles, formatCaption = _a.formatters.formatCaption;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: classNames.caption_label, style: styles.caption_label, \"aria-live\": \"polite\", role: \"presentation\", id: props.id, children: formatCaption(props.displayMonth, { locale: locale }) }));\n}\n\n/**\n * Render the icon in the styled drop-down.\n */\nfunction IconDropdown(props) {\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", __assign({ width: \"8px\", height: \"8px\", viewBox: \"0 0 120 120\", \"data-testid\": \"iconDropdown\" }, props, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { d: \"M4.22182541,48.2218254 C8.44222828,44.0014225 15.2388494,43.9273804 19.5496459,47.9996989 L19.7781746,48.2218254 L60,88.443 L100.221825,48.2218254 C104.442228,44.0014225 111.238849,43.9273804 115.549646,47.9996989 L115.778175,48.2218254 C119.998577,52.4422283 120.07262,59.2388494 116.000301,63.5496459 L115.778175,63.7781746 L67.7781746,111.778175 C63.5577717,115.998577 56.7611506,116.07262 52.4503541,112.000301 L52.2218254,111.778175 L4.22182541,63.7781746 C-0.0739418023,59.4824074 -0.0739418023,52.5175926 4.22182541,48.2218254 Z\", fill: \"currentColor\", fillRule: \"nonzero\" }) })));\n}\n\n/**\n * Render a styled select component – displaying a caption and a custom\n * drop-down icon.\n */\nfunction Dropdown(props) {\n    var _a, _b;\n    var onChange = props.onChange, value = props.value, children = props.children, caption = props.caption, className = props.className, style = props.style;\n    var dayPicker = useDayPicker();\n    var IconDropdownComponent = (_b = (_a = dayPicker.components) === null || _a === void 0 ? void 0 : _a.IconDropdown) !== null && _b !== void 0 ? _b : IconDropdown;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { className: className, style: style, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", { className: dayPicker.classNames.vhidden, children: props['aria-label'] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"select\", { name: props.name, \"aria-label\": props['aria-label'], className: dayPicker.classNames.dropdown, style: dayPicker.styles.dropdown, value: value, onChange: onChange, children: children }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { className: dayPicker.classNames.caption_label, style: dayPicker.styles.caption_label, \"aria-hidden\": \"true\", children: [caption, (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconDropdownComponent, { className: dayPicker.classNames.dropdown_icon, style: dayPicker.styles.dropdown_icon })] })] }));\n}\n\n/** Render the dropdown to navigate between months. */\nfunction MonthsDropdown(props) {\n    var _a;\n    var _b = useDayPicker(), fromDate = _b.fromDate, toDate = _b.toDate, styles = _b.styles, locale = _b.locale, formatMonthCaption = _b.formatters.formatMonthCaption, classNames = _b.classNames, components = _b.components, labelMonthDropdown = _b.labels.labelMonthDropdown;\n    // Dropdown should appear only when both from/toDate is set\n    if (!fromDate)\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});\n    if (!toDate)\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});\n    var dropdownMonths = [];\n    if ((0,date_fns__WEBPACK_IMPORTED_MODULE_7__.isSameYear)(fromDate, toDate)) {\n        // only display the months included in the range\n        var date = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(fromDate);\n        for (var month = fromDate.getMonth(); month <= toDate.getMonth(); month++) {\n            dropdownMonths.push((0,date_fns__WEBPACK_IMPORTED_MODULE_8__.setMonth)(date, month));\n        }\n    }\n    else {\n        // display all the 12 months\n        var date = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(new Date()); // Any date should be OK, as we just need the year\n        for (var month = 0; month <= 11; month++) {\n            dropdownMonths.push((0,date_fns__WEBPACK_IMPORTED_MODULE_8__.setMonth)(date, month));\n        }\n    }\n    var handleChange = function (e) {\n        var selectedMonth = Number(e.target.value);\n        var newMonth = (0,date_fns__WEBPACK_IMPORTED_MODULE_8__.setMonth)((0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(props.displayMonth), selectedMonth);\n        props.onChange(newMonth);\n    };\n    var DropdownComponent = (_a = components === null || components === void 0 ? void 0 : components.Dropdown) !== null && _a !== void 0 ? _a : Dropdown;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DropdownComponent, { name: \"months\", \"aria-label\": labelMonthDropdown(), className: classNames.dropdown_month, style: styles.dropdown_month, onChange: handleChange, value: props.displayMonth.getMonth(), caption: formatMonthCaption(props.displayMonth, { locale: locale }), children: dropdownMonths.map(function (m) { return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"option\", { value: m.getMonth(), children: formatMonthCaption(m, { locale: locale }) }, m.getMonth())); }) }));\n}\n\n/**\n * Render a dropdown to change the year. Take in account the `nav.fromDate` and\n * `toDate` from context.\n */\nfunction YearsDropdown(props) {\n    var _a;\n    var displayMonth = props.displayMonth;\n    var _b = useDayPicker(), fromDate = _b.fromDate, toDate = _b.toDate, locale = _b.locale, styles = _b.styles, classNames = _b.classNames, components = _b.components, formatYearCaption = _b.formatters.formatYearCaption, labelYearDropdown = _b.labels.labelYearDropdown;\n    var years = [];\n    // Dropdown should appear only when both from/toDate is set\n    if (!fromDate)\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});\n    if (!toDate)\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});\n    var fromYear = fromDate.getFullYear();\n    var toYear = toDate.getFullYear();\n    for (var year = fromYear; year <= toYear; year++) {\n        years.push((0,date_fns__WEBPACK_IMPORTED_MODULE_9__.setYear)((0,date_fns__WEBPACK_IMPORTED_MODULE_10__.startOfYear)(new Date()), year));\n    }\n    var handleChange = function (e) {\n        var newMonth = (0,date_fns__WEBPACK_IMPORTED_MODULE_9__.setYear)((0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(displayMonth), Number(e.target.value));\n        props.onChange(newMonth);\n    };\n    var DropdownComponent = (_a = components === null || components === void 0 ? void 0 : components.Dropdown) !== null && _a !== void 0 ? _a : Dropdown;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DropdownComponent, { name: \"years\", \"aria-label\": labelYearDropdown(), className: classNames.dropdown_year, style: styles.dropdown_year, onChange: handleChange, value: displayMonth.getFullYear(), caption: formatYearCaption(displayMonth, { locale: locale }), children: years.map(function (year) { return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"option\", { value: year.getFullYear(), children: formatYearCaption(year, { locale: locale }) }, year.getFullYear())); }) }));\n}\n\n/**\n * Helper hook for using controlled/uncontrolled values from a component props.\n *\n * When the value is not controlled, pass `undefined` as `controlledValue` and\n * use the returned setter to update it.\n *\n * When the value is controlled, pass the controlled value as second\n * argument, which will be always returned as `value`.\n */\nfunction useControlledValue(defaultValue, controlledValue) {\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultValue), uncontrolledValue = _a[0], setValue = _a[1];\n    var value = controlledValue === undefined ? uncontrolledValue : controlledValue;\n    return [value, setValue];\n}\n\n/** Return the initial month according to the given options. */\nfunction getInitialMonth(context) {\n    var month = context.month, defaultMonth = context.defaultMonth, today = context.today;\n    var initialMonth = month || defaultMonth || today || new Date();\n    var toDate = context.toDate, fromDate = context.fromDate, _a = context.numberOfMonths, numberOfMonths = _a === void 0 ? 1 : _a;\n    // Fix the initialMonth if is after the to-date\n    if (toDate && (0,date_fns__WEBPACK_IMPORTED_MODULE_11__.differenceInCalendarMonths)(toDate, initialMonth) < 0) {\n        var offset = -1 * (numberOfMonths - 1);\n        initialMonth = (0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(toDate, offset);\n    }\n    // Fix the initialMonth if is before the from-date\n    if (fromDate && (0,date_fns__WEBPACK_IMPORTED_MODULE_11__.differenceInCalendarMonths)(initialMonth, fromDate) < 0) {\n        initialMonth = fromDate;\n    }\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(initialMonth);\n}\n\n/** Controls the navigation state. */\nfunction useNavigationState() {\n    var context = useDayPicker();\n    var initialMonth = getInitialMonth(context);\n    var _a = useControlledValue(initialMonth, context.month), month = _a[0], setMonth = _a[1];\n    var goToMonth = function (date) {\n        var _a;\n        if (context.disableNavigation)\n            return;\n        var month = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(date);\n        setMonth(month);\n        (_a = context.onMonthChange) === null || _a === void 0 ? void 0 : _a.call(context, month);\n    };\n    return [month, goToMonth];\n}\n\n/**\n * Return the months to display in the component according to the number of\n * months and the from/to date.\n */\nfunction getDisplayMonths(month, _a) {\n    var reverseMonths = _a.reverseMonths, numberOfMonths = _a.numberOfMonths;\n    var start = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(month);\n    var end = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)((0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(start, numberOfMonths));\n    var monthsDiff = (0,date_fns__WEBPACK_IMPORTED_MODULE_11__.differenceInCalendarMonths)(end, start);\n    var months = [];\n    for (var i = 0; i < monthsDiff; i++) {\n        var nextMonth = (0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(start, i);\n        months.push(nextMonth);\n    }\n    if (reverseMonths)\n        months = months.reverse();\n    return months;\n}\n\n/**\n * Returns the next month the user can navigate to according to the given\n * options.\n *\n * Please note that the next month is not always the next calendar month:\n *\n * - if after the `toDate` range, is undefined;\n * - if the navigation is paged, is the number of months displayed ahead.\n *\n */\nfunction getNextMonth(startingMonth, options) {\n    if (options.disableNavigation) {\n        return undefined;\n    }\n    var toDate = options.toDate, pagedNavigation = options.pagedNavigation, _a = options.numberOfMonths, numberOfMonths = _a === void 0 ? 1 : _a;\n    var offset = pagedNavigation ? numberOfMonths : 1;\n    var month = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(startingMonth);\n    if (!toDate) {\n        return (0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(month, offset);\n    }\n    var monthsDiff = (0,date_fns__WEBPACK_IMPORTED_MODULE_11__.differenceInCalendarMonths)(toDate, startingMonth);\n    if (monthsDiff < numberOfMonths) {\n        return undefined;\n    }\n    // Jump forward as the number of months when paged navigation\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(month, offset);\n}\n\n/**\n * Returns the next previous the user can navigate to, according to the given\n * options.\n *\n * Please note that the previous month is not always the previous calendar\n * month:\n *\n * - if before the `fromDate` date, is `undefined`;\n * - if the navigation is paged, is the number of months displayed before.\n *\n */\nfunction getPreviousMonth(startingMonth, options) {\n    if (options.disableNavigation) {\n        return undefined;\n    }\n    var fromDate = options.fromDate, pagedNavigation = options.pagedNavigation, _a = options.numberOfMonths, numberOfMonths = _a === void 0 ? 1 : _a;\n    var offset = pagedNavigation ? numberOfMonths : 1;\n    var month = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(startingMonth);\n    if (!fromDate) {\n        return (0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(month, -offset);\n    }\n    var monthsDiff = (0,date_fns__WEBPACK_IMPORTED_MODULE_11__.differenceInCalendarMonths)(month, fromDate);\n    if (monthsDiff <= 0) {\n        return undefined;\n    }\n    // Jump back as the number of months when paged navigation\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(month, -offset);\n}\n\n/**\n * The Navigation context shares details and methods to navigate the months in DayPicker.\n * Access this context from the {@link useNavigation} hook.\n */\nvar NavigationContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/** Provides the values for the {@link NavigationContext}. */\nfunction NavigationProvider(props) {\n    var dayPicker = useDayPicker();\n    var _a = useNavigationState(), currentMonth = _a[0], goToMonth = _a[1];\n    var displayMonths = getDisplayMonths(currentMonth, dayPicker);\n    var nextMonth = getNextMonth(currentMonth, dayPicker);\n    var previousMonth = getPreviousMonth(currentMonth, dayPicker);\n    var isDateDisplayed = function (date) {\n        return displayMonths.some(function (displayMonth) {\n            return (0,date_fns__WEBPACK_IMPORTED_MODULE_13__.isSameMonth)(date, displayMonth);\n        });\n    };\n    var goToDate = function (date, refDate) {\n        if (isDateDisplayed(date)) {\n            return;\n        }\n        if (refDate && (0,date_fns__WEBPACK_IMPORTED_MODULE_14__.isBefore)(date, refDate)) {\n            goToMonth((0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(date, 1 + dayPicker.numberOfMonths * -1));\n        }\n        else {\n            goToMonth(date);\n        }\n    };\n    var value = {\n        currentMonth: currentMonth,\n        displayMonths: displayMonths,\n        goToMonth: goToMonth,\n        goToDate: goToDate,\n        previousMonth: previousMonth,\n        nextMonth: nextMonth,\n        isDateDisplayed: isDateDisplayed\n    };\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(NavigationContext.Provider, { value: value, children: props.children }));\n}\n/**\n * Hook to access the {@link NavigationContextValue}. Use this hook to navigate\n * between months or years in DayPicker.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useNavigation() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NavigationContext);\n    if (!context) {\n        throw new Error('useNavigation must be used within a NavigationProvider');\n    }\n    return context;\n}\n\n/**\n * Render a caption with the dropdowns to navigate between months and years.\n */\nfunction CaptionDropdowns(props) {\n    var _a;\n    var _b = useDayPicker(), classNames = _b.classNames, styles = _b.styles, components = _b.components;\n    var goToMonth = useNavigation().goToMonth;\n    var handleMonthChange = function (newMonth) {\n        goToMonth((0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(newMonth, props.displayIndex ? -props.displayIndex : 0));\n    };\n    var CaptionLabelComponent = (_a = components === null || components === void 0 ? void 0 : components.CaptionLabel) !== null && _a !== void 0 ? _a : CaptionLabel;\n    var captionLabel = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionLabelComponent, { id: props.id, displayMonth: props.displayMonth }));\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { className: classNames.caption_dropdowns, style: styles.caption_dropdowns, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: classNames.vhidden, children: captionLabel }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(MonthsDropdown, { onChange: handleMonthChange, displayMonth: props.displayMonth }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(YearsDropdown, { onChange: handleMonthChange, displayMonth: props.displayMonth })] }));\n}\n\n/**\n * Render the \"previous month\" button in the navigation.\n */\nfunction IconLeft(props) {\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", __assign({ width: \"16px\", height: \"16px\", viewBox: \"0 0 120 120\" }, props, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { d: \"M69.490332,3.34314575 C72.6145263,0.218951416 77.6798462,0.218951416 80.8040405,3.34314575 C83.8617626,6.40086786 83.9268205,11.3179931 80.9992143,14.4548388 L80.8040405,14.6568542 L35.461,60 L80.8040405,105.343146 C83.8617626,108.400868 83.9268205,113.317993 80.9992143,116.454839 L80.8040405,116.656854 C77.7463184,119.714576 72.8291931,119.779634 69.6923475,116.852028 L69.490332,116.656854 L18.490332,65.6568542 C15.4326099,62.5991321 15.367552,57.6820069 18.2951583,54.5451612 L18.490332,54.3431458 L69.490332,3.34314575 Z\", fill: \"currentColor\", fillRule: \"nonzero\" }) })));\n}\n\n/**\n * Render the \"next month\" button in the navigation.\n */\nfunction IconRight(props) {\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", __assign({ width: \"16px\", height: \"16px\", viewBox: \"0 0 120 120\" }, props, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { d: \"M49.8040405,3.34314575 C46.6798462,0.218951416 41.6145263,0.218951416 38.490332,3.34314575 C35.4326099,6.40086786 35.367552,11.3179931 38.2951583,14.4548388 L38.490332,14.6568542 L83.8333725,60 L38.490332,105.343146 C35.4326099,108.400868 35.367552,113.317993 38.2951583,116.454839 L38.490332,116.656854 C41.5480541,119.714576 46.4651794,119.779634 49.602025,116.852028 L49.8040405,116.656854 L100.804041,65.6568542 C103.861763,62.5991321 103.926821,57.6820069 100.999214,54.5451612 L100.804041,54.3431458 L49.8040405,3.34314575 Z\", fill: \"currentColor\" }) })));\n}\n\n/** Render a button HTML element applying the reset class name. */\nvar Button = (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(function (props, ref) {\n    var _a = useDayPicker(), classNames = _a.classNames, styles = _a.styles;\n    var classNamesArr = [classNames.button_reset, classNames.button];\n    if (props.className) {\n        classNamesArr.push(props.className);\n    }\n    var className = classNamesArr.join(' ');\n    var style = __assign(__assign({}, styles.button_reset), styles.button);\n    if (props.style) {\n        Object.assign(style, props.style);\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"button\", __assign({}, props, { ref: ref, type: \"button\", className: className, style: style })));\n});\n\n/** A component rendering the navigation buttons or the drop-downs. */\nfunction Navigation(props) {\n    var _a, _b;\n    var _c = useDayPicker(), dir = _c.dir, locale = _c.locale, classNames = _c.classNames, styles = _c.styles, _d = _c.labels, labelPrevious = _d.labelPrevious, labelNext = _d.labelNext, components = _c.components;\n    if (!props.nextMonth && !props.previousMonth) {\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});\n    }\n    var previousLabel = labelPrevious(props.previousMonth, { locale: locale });\n    var previousClassName = [\n        classNames.nav_button,\n        classNames.nav_button_previous\n    ].join(' ');\n    var nextLabel = labelNext(props.nextMonth, { locale: locale });\n    var nextClassName = [\n        classNames.nav_button,\n        classNames.nav_button_next\n    ].join(' ');\n    var IconRightComponent = (_a = components === null || components === void 0 ? void 0 : components.IconRight) !== null && _a !== void 0 ? _a : IconRight;\n    var IconLeftComponent = (_b = components === null || components === void 0 ? void 0 : components.IconLeft) !== null && _b !== void 0 ? _b : IconLeft;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { className: classNames.nav, style: styles.nav, children: [!props.hidePrevious && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Button, { name: \"previous-month\", \"aria-label\": previousLabel, className: previousClassName, style: styles.nav_button_previous, disabled: !props.previousMonth, onClick: props.onPreviousClick, children: dir === 'rtl' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconRightComponent, { className: classNames.nav_icon, style: styles.nav_icon })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconLeftComponent, { className: classNames.nav_icon, style: styles.nav_icon })) })), !props.hideNext && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Button, { name: \"next-month\", \"aria-label\": nextLabel, className: nextClassName, style: styles.nav_button_next, disabled: !props.nextMonth, onClick: props.onNextClick, children: dir === 'rtl' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconLeftComponent, { className: classNames.nav_icon, style: styles.nav_icon })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconRightComponent, { className: classNames.nav_icon, style: styles.nav_icon })) }))] }));\n}\n\n/**\n * Render a caption with a button-based navigation.\n */\nfunction CaptionNavigation(props) {\n    var numberOfMonths = useDayPicker().numberOfMonths;\n    var _a = useNavigation(), previousMonth = _a.previousMonth, nextMonth = _a.nextMonth, goToMonth = _a.goToMonth, displayMonths = _a.displayMonths;\n    var displayIndex = displayMonths.findIndex(function (month) {\n        return (0,date_fns__WEBPACK_IMPORTED_MODULE_13__.isSameMonth)(props.displayMonth, month);\n    });\n    var isFirst = displayIndex === 0;\n    var isLast = displayIndex === displayMonths.length - 1;\n    var hideNext = numberOfMonths > 1 && (isFirst || !isLast);\n    var hidePrevious = numberOfMonths > 1 && (isLast || !isFirst);\n    var handlePreviousClick = function () {\n        if (!previousMonth)\n            return;\n        goToMonth(previousMonth);\n    };\n    var handleNextClick = function () {\n        if (!nextMonth)\n            return;\n        goToMonth(nextMonth);\n    };\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Navigation, { displayMonth: props.displayMonth, hideNext: hideNext, hidePrevious: hidePrevious, nextMonth: nextMonth, previousMonth: previousMonth, onPreviousClick: handlePreviousClick, onNextClick: handleNextClick }));\n}\n\n/**\n * Render the caption of a month. The caption has a different layout when\n * setting the {@link DayPickerBase.captionLayout} prop.\n */\nfunction Caption(props) {\n    var _a;\n    var _b = useDayPicker(), classNames = _b.classNames, disableNavigation = _b.disableNavigation, styles = _b.styles, captionLayout = _b.captionLayout, components = _b.components;\n    var CaptionLabelComponent = (_a = components === null || components === void 0 ? void 0 : components.CaptionLabel) !== null && _a !== void 0 ? _a : CaptionLabel;\n    var caption;\n    if (disableNavigation) {\n        caption = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionLabelComponent, { id: props.id, displayMonth: props.displayMonth }));\n    }\n    else if (captionLayout === 'dropdown') {\n        caption = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionDropdowns, { displayMonth: props.displayMonth, id: props.id }));\n    }\n    else if (captionLayout === 'dropdown-buttons') {\n        caption = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionDropdowns, { displayMonth: props.displayMonth, displayIndex: props.displayIndex, id: props.id }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionNavigation, { displayMonth: props.displayMonth, displayIndex: props.displayIndex, id: props.id })] }));\n    }\n    else {\n        caption = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionLabelComponent, { id: props.id, displayMonth: props.displayMonth, displayIndex: props.displayIndex }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionNavigation, { displayMonth: props.displayMonth, id: props.id })] }));\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: classNames.caption, style: styles.caption, children: caption }));\n}\n\n/** Render the Footer component (empty as default).*/\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction Footer(props) {\n    var _a = useDayPicker(), footer = _a.footer, styles = _a.styles, tfoot = _a.classNames.tfoot;\n    if (!footer)\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"tfoot\", { className: tfoot, style: styles.tfoot, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"tr\", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { colSpan: 8, children: footer }) }) }));\n}\n\n/**\n * Generate a series of 7 days, starting from the week, to use for formatting\n * the weekday names (Monday, Tuesday, etc.).\n */\nfunction getWeekdays(locale, \n/** The index of the first day of the week (0 - Sunday). */\nweekStartsOn, \n/** Use ISOWeek instead of locale/ */\nISOWeek) {\n    var start = ISOWeek\n        ? (0,date_fns__WEBPACK_IMPORTED_MODULE_15__.startOfISOWeek)(new Date())\n        : (0,date_fns__WEBPACK_IMPORTED_MODULE_16__.startOfWeek)(new Date(), { locale: locale, weekStartsOn: weekStartsOn });\n    var days = [];\n    for (var i = 0; i < 7; i++) {\n        var day = (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(start, i);\n        days.push(day);\n    }\n    return days;\n}\n\n/**\n * Render the HeadRow component - i.e. the table head row with the weekday names.\n */\nfunction HeadRow() {\n    var _a = useDayPicker(), classNames = _a.classNames, styles = _a.styles, showWeekNumber = _a.showWeekNumber, locale = _a.locale, weekStartsOn = _a.weekStartsOn, ISOWeek = _a.ISOWeek, formatWeekdayName = _a.formatters.formatWeekdayName, labelWeekday = _a.labels.labelWeekday;\n    var weekdays = getWeekdays(locale, weekStartsOn, ISOWeek);\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"tr\", { style: styles.head_row, className: classNames.head_row, children: [showWeekNumber && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { style: styles.head_cell, className: classNames.head_cell })), weekdays.map(function (weekday, i) { return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"th\", { scope: \"col\", className: classNames.head_cell, style: styles.head_cell, \"aria-label\": labelWeekday(weekday, { locale: locale }), children: formatWeekdayName(weekday, { locale: locale }) }, i)); })] }));\n}\n\n/** Render the table head. */\nfunction Head() {\n    var _a;\n    var _b = useDayPicker(), classNames = _b.classNames, styles = _b.styles, components = _b.components;\n    var HeadRowComponent = (_a = components === null || components === void 0 ? void 0 : components.HeadRow) !== null && _a !== void 0 ? _a : HeadRow;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"thead\", { style: styles.head, className: classNames.head, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(HeadRowComponent, {}) }));\n}\n\n/** Render the content of the day cell. */\nfunction DayContent(props) {\n    var _a = useDayPicker(), locale = _a.locale, formatDay = _a.formatters.formatDay;\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: formatDay(props.date, { locale: locale }) });\n}\n\n/**\n * The SelectMultiple context shares details about the selected days when in\n * multiple selection mode.\n *\n * Access this context from the {@link useSelectMultiple} hook.\n */\nvar SelectMultipleContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/** Provides the values for the {@link SelectMultipleContext}. */\nfunction SelectMultipleProvider(props) {\n    if (!isDayPickerMultiple(props.initialProps)) {\n        var emptyContextValue = {\n            selected: undefined,\n            modifiers: {\n                disabled: []\n            }\n        };\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectMultipleContext.Provider, { value: emptyContextValue, children: props.children }));\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectMultipleProviderInternal, { initialProps: props.initialProps, children: props.children }));\n}\nfunction SelectMultipleProviderInternal(_a) {\n    var initialProps = _a.initialProps, children = _a.children;\n    var selected = initialProps.selected, min = initialProps.min, max = initialProps.max;\n    var onDayClick = function (day, activeModifiers, e) {\n        var _a, _b;\n        (_a = initialProps.onDayClick) === null || _a === void 0 ? void 0 : _a.call(initialProps, day, activeModifiers, e);\n        var isMinSelected = Boolean(activeModifiers.selected && min && (selected === null || selected === void 0 ? void 0 : selected.length) === min);\n        if (isMinSelected) {\n            return;\n        }\n        var isMaxSelected = Boolean(!activeModifiers.selected && max && (selected === null || selected === void 0 ? void 0 : selected.length) === max);\n        if (isMaxSelected) {\n            return;\n        }\n        var selectedDays = selected ? __spreadArray([], selected, true) : [];\n        if (activeModifiers.selected) {\n            var index = selectedDays.findIndex(function (selectedDay) {\n                return (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(day, selectedDay);\n            });\n            selectedDays.splice(index, 1);\n        }\n        else {\n            selectedDays.push(day);\n        }\n        (_b = initialProps.onSelect) === null || _b === void 0 ? void 0 : _b.call(initialProps, selectedDays, day, activeModifiers, e);\n    };\n    var modifiers = {\n        disabled: []\n    };\n    if (selected) {\n        modifiers.disabled.push(function (day) {\n            var isMaxSelected = max && selected.length > max - 1;\n            var isSelected = selected.some(function (selectedDay) {\n                return (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(selectedDay, day);\n            });\n            return Boolean(isMaxSelected && !isSelected);\n        });\n    }\n    var contextValue = {\n        selected: selected,\n        onDayClick: onDayClick,\n        modifiers: modifiers\n    };\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectMultipleContext.Provider, { value: contextValue, children: children }));\n}\n/**\n * Hook to access the {@link SelectMultipleContextValue}.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useSelectMultiple() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SelectMultipleContext);\n    if (!context) {\n        throw new Error('useSelectMultiple must be used within a SelectMultipleProvider');\n    }\n    return context;\n}\n\n/**\n * Add a day to an existing range.\n *\n * The returned range takes in account the `undefined` values and if the added\n * day is already present in the range.\n */\nfunction addToRange(day, range) {\n    var _a = range || {}, from = _a.from, to = _a.to;\n    if (from && to) {\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(to, day) && (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(from, day)) {\n            return undefined;\n        }\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(to, day)) {\n            return { from: to, to: undefined };\n        }\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(from, day)) {\n            return undefined;\n        }\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_19__.isAfter)(from, day)) {\n            return { from: day, to: to };\n        }\n        return { from: from, to: day };\n    }\n    if (to) {\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_19__.isAfter)(day, to)) {\n            return { from: to, to: day };\n        }\n        return { from: day, to: to };\n    }\n    if (from) {\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_14__.isBefore)(day, from)) {\n            return { from: day, to: from };\n        }\n        return { from: from, to: day };\n    }\n    return { from: day, to: undefined };\n}\n\n/**\n * The SelectRange context shares details about the selected days when in\n * range selection mode.\n *\n * Access this context from the {@link useSelectRange} hook.\n */\nvar SelectRangeContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/** Provides the values for the {@link SelectRangeProvider}. */\nfunction SelectRangeProvider(props) {\n    if (!isDayPickerRange(props.initialProps)) {\n        var emptyContextValue = {\n            selected: undefined,\n            modifiers: {\n                range_start: [],\n                range_end: [],\n                range_middle: [],\n                disabled: []\n            }\n        };\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectRangeContext.Provider, { value: emptyContextValue, children: props.children }));\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectRangeProviderInternal, { initialProps: props.initialProps, children: props.children }));\n}\nfunction SelectRangeProviderInternal(_a) {\n    var initialProps = _a.initialProps, children = _a.children;\n    var selected = initialProps.selected;\n    var _b = selected || {}, selectedFrom = _b.from, selectedTo = _b.to;\n    var min = initialProps.min;\n    var max = initialProps.max;\n    var onDayClick = function (day, activeModifiers, e) {\n        var _a, _b;\n        (_a = initialProps.onDayClick) === null || _a === void 0 ? void 0 : _a.call(initialProps, day, activeModifiers, e);\n        var newRange = addToRange(day, selected);\n        (_b = initialProps.onSelect) === null || _b === void 0 ? void 0 : _b.call(initialProps, newRange, day, activeModifiers, e);\n    };\n    var modifiers = {\n        range_start: [],\n        range_end: [],\n        range_middle: [],\n        disabled: []\n    };\n    if (selectedFrom) {\n        modifiers.range_start = [selectedFrom];\n        if (!selectedTo) {\n            modifiers.range_end = [selectedFrom];\n        }\n        else {\n            modifiers.range_end = [selectedTo];\n            if (!(0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(selectedFrom, selectedTo)) {\n                modifiers.range_middle = [\n                    {\n                        after: selectedFrom,\n                        before: selectedTo\n                    }\n                ];\n            }\n        }\n    }\n    else if (selectedTo) {\n        modifiers.range_start = [selectedTo];\n        modifiers.range_end = [selectedTo];\n    }\n    if (min) {\n        if (selectedFrom && !selectedTo) {\n            modifiers.disabled.push({\n                after: (0,date_fns__WEBPACK_IMPORTED_MODULE_20__.subDays)(selectedFrom, min - 1),\n                before: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedFrom, min - 1)\n            });\n        }\n        if (selectedFrom && selectedTo) {\n            modifiers.disabled.push({\n                after: selectedFrom,\n                before: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedFrom, min - 1)\n            });\n        }\n        if (!selectedFrom && selectedTo) {\n            modifiers.disabled.push({\n                after: (0,date_fns__WEBPACK_IMPORTED_MODULE_20__.subDays)(selectedTo, min - 1),\n                before: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedTo, min - 1)\n            });\n        }\n    }\n    if (max) {\n        if (selectedFrom && !selectedTo) {\n            modifiers.disabled.push({\n                before: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedFrom, -max + 1)\n            });\n            modifiers.disabled.push({\n                after: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedFrom, max - 1)\n            });\n        }\n        if (selectedFrom && selectedTo) {\n            var selectedCount = (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(selectedTo, selectedFrom) + 1;\n            var offset = max - selectedCount;\n            modifiers.disabled.push({\n                before: (0,date_fns__WEBPACK_IMPORTED_MODULE_20__.subDays)(selectedFrom, offset)\n            });\n            modifiers.disabled.push({\n                after: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedTo, offset)\n            });\n        }\n        if (!selectedFrom && selectedTo) {\n            modifiers.disabled.push({\n                before: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedTo, -max + 1)\n            });\n            modifiers.disabled.push({\n                after: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedTo, max - 1)\n            });\n        }\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectRangeContext.Provider, { value: { selected: selected, onDayClick: onDayClick, modifiers: modifiers }, children: children }));\n}\n/**\n * Hook to access the {@link SelectRangeContextValue}.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useSelectRange() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SelectRangeContext);\n    if (!context) {\n        throw new Error('useSelectRange must be used within a SelectRangeProvider');\n    }\n    return context;\n}\n\n/** Normalize to array a matcher input. */\nfunction matcherToArray(matcher) {\n    if (Array.isArray(matcher)) {\n        return __spreadArray([], matcher, true);\n    }\n    else if (matcher !== undefined) {\n        return [matcher];\n    }\n    else {\n        return [];\n    }\n}\n\n/** Create CustomModifiers from dayModifiers */\nfunction getCustomModifiers(dayModifiers) {\n    var customModifiers = {};\n    Object.entries(dayModifiers).forEach(function (_a) {\n        var modifier = _a[0], matcher = _a[1];\n        customModifiers[modifier] = matcherToArray(matcher);\n    });\n    return customModifiers;\n}\n\n/** The name of the modifiers that are used internally by DayPicker. */\nvar InternalModifier;\n(function (InternalModifier) {\n    InternalModifier[\"Outside\"] = \"outside\";\n    /** Name of the modifier applied to the disabled days, using the `disabled` prop. */\n    InternalModifier[\"Disabled\"] = \"disabled\";\n    /** Name of the modifier applied to the selected days using the `selected` prop). */\n    InternalModifier[\"Selected\"] = \"selected\";\n    /** Name of the modifier applied to the hidden days using the `hidden` prop). */\n    InternalModifier[\"Hidden\"] = \"hidden\";\n    /** Name of the modifier applied to the day specified using the `today` prop). */\n    InternalModifier[\"Today\"] = \"today\";\n    /** The modifier applied to the day starting a selected range, when in range selection mode.  */\n    InternalModifier[\"RangeStart\"] = \"range_start\";\n    /** The modifier applied to the day ending a selected range, when in range selection mode.  */\n    InternalModifier[\"RangeEnd\"] = \"range_end\";\n    /** The modifier applied to the days between the start and the end of a selected range, when in range selection mode.  */\n    InternalModifier[\"RangeMiddle\"] = \"range_middle\";\n})(InternalModifier || (InternalModifier = {}));\n\nvar Selected = InternalModifier.Selected, Disabled = InternalModifier.Disabled, Hidden = InternalModifier.Hidden, Today = InternalModifier.Today, RangeEnd = InternalModifier.RangeEnd, RangeMiddle = InternalModifier.RangeMiddle, RangeStart = InternalModifier.RangeStart, Outside = InternalModifier.Outside;\n/** Return the {@link InternalModifiers} from the DayPicker and select contexts. */\nfunction getInternalModifiers(dayPicker, selectMultiple, selectRange) {\n    var _a;\n    var internalModifiers = (_a = {},\n        _a[Selected] = matcherToArray(dayPicker.selected),\n        _a[Disabled] = matcherToArray(dayPicker.disabled),\n        _a[Hidden] = matcherToArray(dayPicker.hidden),\n        _a[Today] = [dayPicker.today],\n        _a[RangeEnd] = [],\n        _a[RangeMiddle] = [],\n        _a[RangeStart] = [],\n        _a[Outside] = [],\n        _a);\n    if (dayPicker.fromDate) {\n        internalModifiers[Disabled].push({ before: dayPicker.fromDate });\n    }\n    if (dayPicker.toDate) {\n        internalModifiers[Disabled].push({ after: dayPicker.toDate });\n    }\n    if (isDayPickerMultiple(dayPicker)) {\n        internalModifiers[Disabled] = internalModifiers[Disabled].concat(selectMultiple.modifiers[Disabled]);\n    }\n    else if (isDayPickerRange(dayPicker)) {\n        internalModifiers[Disabled] = internalModifiers[Disabled].concat(selectRange.modifiers[Disabled]);\n        internalModifiers[RangeStart] = selectRange.modifiers[RangeStart];\n        internalModifiers[RangeMiddle] = selectRange.modifiers[RangeMiddle];\n        internalModifiers[RangeEnd] = selectRange.modifiers[RangeEnd];\n    }\n    return internalModifiers;\n}\n\n/** The Modifiers context store the modifiers used in DayPicker. To access the value of this context, use {@link useModifiers}. */\nvar ModifiersContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/** Provide the value for the {@link ModifiersContext}. */\nfunction ModifiersProvider(props) {\n    var dayPicker = useDayPicker();\n    var selectMultiple = useSelectMultiple();\n    var selectRange = useSelectRange();\n    var internalModifiers = getInternalModifiers(dayPicker, selectMultiple, selectRange);\n    var customModifiers = getCustomModifiers(dayPicker.modifiers);\n    var modifiers = __assign(__assign({}, internalModifiers), customModifiers);\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ModifiersContext.Provider, { value: modifiers, children: props.children }));\n}\n/**\n * Return the modifiers used by DayPicker.\n *\n * This hook is meant to be used inside internal or custom components.\n * Requires to be wrapped into {@link ModifiersProvider}.\n *\n */\nfunction useModifiers() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ModifiersContext);\n    if (!context) {\n        throw new Error('useModifiers must be used within a ModifiersProvider');\n    }\n    return context;\n}\n\n/** Returns true if `matcher` is of type {@link DateInterval}. */\nfunction isDateInterval(matcher) {\n    return Boolean(matcher &&\n        typeof matcher === 'object' &&\n        'before' in matcher &&\n        'after' in matcher);\n}\n/** Returns true if `value` is a {@link DateRange} type. */\nfunction isDateRange(value) {\n    return Boolean(value && typeof value === 'object' && 'from' in value);\n}\n/** Returns true if `value` is of type {@link DateAfter}. */\nfunction isDateAfterType(value) {\n    return Boolean(value && typeof value === 'object' && 'after' in value);\n}\n/** Returns true if `value` is of type {@link DateBefore}. */\nfunction isDateBeforeType(value) {\n    return Boolean(value && typeof value === 'object' && 'before' in value);\n}\n/** Returns true if `value` is a {@link DayOfWeek} type. */\nfunction isDayOfWeekType(value) {\n    return Boolean(value && typeof value === 'object' && 'dayOfWeek' in value);\n}\n\n/** Return `true` whether `date` is inside `range`. */\nfunction isDateInRange(date, range) {\n    var _a;\n    var from = range.from, to = range.to;\n    if (from && to) {\n        var isRangeInverted = (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(to, from) < 0;\n        if (isRangeInverted) {\n            _a = [to, from], from = _a[0], to = _a[1];\n        }\n        var isInRange = (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(date, from) >= 0 &&\n            (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(to, date) >= 0;\n        return isInRange;\n    }\n    if (to) {\n        return (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(to, date);\n    }\n    if (from) {\n        return (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(from, date);\n    }\n    return false;\n}\n\n/** Returns true if `value` is a Date type. */\nfunction isDateType(value) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_22__.isDate)(value);\n}\n/** Returns true if `value` is an array of valid dates. */\nfunction isArrayOfDates(value) {\n    return Array.isArray(value) && value.every(date_fns__WEBPACK_IMPORTED_MODULE_22__.isDate);\n}\n/**\n * Returns whether a day matches against at least one of the given Matchers.\n *\n * ```\n * const day = new Date(2022, 5, 19);\n * const matcher1: DateRange = {\n *    from: new Date(2021, 12, 21),\n *    to: new Date(2021, 12, 30)\n * }\n * const matcher2: DateRange = {\n *    from: new Date(2022, 5, 1),\n *    to: new Date(2022, 5, 23)\n * }\n *\n * const isMatch(day, [matcher1, matcher2]); // true, since day is in the matcher1 range.\n * ```\n * */\nfunction isMatch(day, matchers) {\n    return matchers.some(function (matcher) {\n        if (typeof matcher === 'boolean') {\n            return matcher;\n        }\n        if (isDateType(matcher)) {\n            return (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(day, matcher);\n        }\n        if (isArrayOfDates(matcher)) {\n            return matcher.includes(day);\n        }\n        if (isDateRange(matcher)) {\n            return isDateInRange(day, matcher);\n        }\n        if (isDayOfWeekType(matcher)) {\n            return matcher.dayOfWeek.includes(day.getDay());\n        }\n        if (isDateInterval(matcher)) {\n            var diffBefore = (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(matcher.before, day);\n            var diffAfter = (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(matcher.after, day);\n            var isDayBefore = diffBefore > 0;\n            var isDayAfter = diffAfter < 0;\n            var isClosedInterval = (0,date_fns__WEBPACK_IMPORTED_MODULE_19__.isAfter)(matcher.before, matcher.after);\n            if (isClosedInterval) {\n                return isDayAfter && isDayBefore;\n            }\n            else {\n                return isDayBefore || isDayAfter;\n            }\n        }\n        if (isDateAfterType(matcher)) {\n            return (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(day, matcher.after) > 0;\n        }\n        if (isDateBeforeType(matcher)) {\n            return (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(matcher.before, day) > 0;\n        }\n        if (typeof matcher === 'function') {\n            return matcher(day);\n        }\n        return false;\n    });\n}\n\n/** Return the active modifiers for the given day. */\nfunction getActiveModifiers(day, \n/** The modifiers to match for the given date. */\nmodifiers, \n/** The month where the day is displayed, to add the \"outside\" modifiers.  */\ndisplayMonth) {\n    var matchedModifiers = Object.keys(modifiers).reduce(function (result, key) {\n        var modifier = modifiers[key];\n        if (isMatch(day, modifier)) {\n            result.push(key);\n        }\n        return result;\n    }, []);\n    var activeModifiers = {};\n    matchedModifiers.forEach(function (modifier) { return (activeModifiers[modifier] = true); });\n    if (displayMonth && !(0,date_fns__WEBPACK_IMPORTED_MODULE_13__.isSameMonth)(day, displayMonth)) {\n        activeModifiers.outside = true;\n    }\n    return activeModifiers;\n}\n\n/**\n * Returns the day that should be the target of the focus when DayPicker is\n * rendered the first time.\n *\n * TODO: this function doesn't consider if the day is outside the month. We\n * implemented this check in `useDayRender` but it should probably go here. See\n * https://github.com/gpbl/react-day-picker/pull/1576\n */\nfunction getInitialFocusTarget(displayMonths, modifiers) {\n    var firstDayInMonth = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(displayMonths[0]);\n    var lastDayInMonth = (0,date_fns__WEBPACK_IMPORTED_MODULE_5__.endOfMonth)(displayMonths[displayMonths.length - 1]);\n    // TODO: cleanup code\n    var firstFocusableDay;\n    var today;\n    var date = firstDayInMonth;\n    while (date <= lastDayInMonth) {\n        var activeModifiers = getActiveModifiers(date, modifiers);\n        var isFocusable = !activeModifiers.disabled && !activeModifiers.hidden;\n        if (!isFocusable) {\n            date = (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(date, 1);\n            continue;\n        }\n        if (activeModifiers.selected) {\n            return date;\n        }\n        if (activeModifiers.today && !today) {\n            today = date;\n        }\n        if (!firstFocusableDay) {\n            firstFocusableDay = date;\n        }\n        date = (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(date, 1);\n    }\n    if (today) {\n        return today;\n    }\n    else {\n        return firstFocusableDay;\n    }\n}\n\nvar MAX_RETRY = 365;\n/** Return the next date to be focused. */\nfunction getNextFocus(focusedDay, options) {\n    var moveBy = options.moveBy, direction = options.direction, context = options.context, modifiers = options.modifiers, _a = options.retry, retry = _a === void 0 ? { count: 0, lastFocused: focusedDay } : _a;\n    var weekStartsOn = context.weekStartsOn, fromDate = context.fromDate, toDate = context.toDate, locale = context.locale;\n    var moveFns = {\n        day: date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays,\n        week: date_fns__WEBPACK_IMPORTED_MODULE_23__.addWeeks,\n        month: date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths,\n        year: date_fns__WEBPACK_IMPORTED_MODULE_24__.addYears,\n        startOfWeek: function (date) {\n            return context.ISOWeek\n                ? (0,date_fns__WEBPACK_IMPORTED_MODULE_15__.startOfISOWeek)(date)\n                : (0,date_fns__WEBPACK_IMPORTED_MODULE_16__.startOfWeek)(date, { locale: locale, weekStartsOn: weekStartsOn });\n        },\n        endOfWeek: function (date) {\n            return context.ISOWeek\n                ? (0,date_fns__WEBPACK_IMPORTED_MODULE_25__.endOfISOWeek)(date)\n                : (0,date_fns__WEBPACK_IMPORTED_MODULE_26__.endOfWeek)(date, { locale: locale, weekStartsOn: weekStartsOn });\n        }\n    };\n    var newFocusedDay = moveFns[moveBy](focusedDay, direction === 'after' ? 1 : -1);\n    if (direction === 'before' && fromDate) {\n        newFocusedDay = (0,date_fns__WEBPACK_IMPORTED_MODULE_27__.max)([fromDate, newFocusedDay]);\n    }\n    else if (direction === 'after' && toDate) {\n        newFocusedDay = (0,date_fns__WEBPACK_IMPORTED_MODULE_28__.min)([toDate, newFocusedDay]);\n    }\n    var isFocusable = true;\n    if (modifiers) {\n        var activeModifiers = getActiveModifiers(newFocusedDay, modifiers);\n        isFocusable = !activeModifiers.disabled && !activeModifiers.hidden;\n    }\n    if (isFocusable) {\n        return newFocusedDay;\n    }\n    else {\n        if (retry.count > MAX_RETRY) {\n            return retry.lastFocused;\n        }\n        return getNextFocus(newFocusedDay, {\n            moveBy: moveBy,\n            direction: direction,\n            context: context,\n            modifiers: modifiers,\n            retry: __assign(__assign({}, retry), { count: retry.count + 1 })\n        });\n    }\n}\n\n/**\n * The Focus context shares details about the focused day for the keyboard\n *\n * Access this context from the {@link useFocusContext} hook.\n */\nvar FocusContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/** The provider for the {@link FocusContext}. */\nfunction FocusProvider(props) {\n    var navigation = useNavigation();\n    var modifiers = useModifiers();\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(), focusedDay = _a[0], setFocusedDay = _a[1];\n    var _b = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(), lastFocused = _b[0], setLastFocused = _b[1];\n    var initialFocusTarget = getInitialFocusTarget(navigation.displayMonths, modifiers);\n    // TODO: cleanup and test obscure code below\n    var focusTarget = (focusedDay !== null && focusedDay !== void 0 ? focusedDay : (lastFocused && navigation.isDateDisplayed(lastFocused)))\n        ? lastFocused\n        : initialFocusTarget;\n    var blur = function () {\n        setLastFocused(focusedDay);\n        setFocusedDay(undefined);\n    };\n    var focus = function (date) {\n        setFocusedDay(date);\n    };\n    var context = useDayPicker();\n    var moveFocus = function (moveBy, direction) {\n        if (!focusedDay)\n            return;\n        var nextFocused = getNextFocus(focusedDay, {\n            moveBy: moveBy,\n            direction: direction,\n            context: context,\n            modifiers: modifiers\n        });\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(focusedDay, nextFocused))\n            return undefined;\n        navigation.goToDate(nextFocused, focusedDay);\n        focus(nextFocused);\n    };\n    var value = {\n        focusedDay: focusedDay,\n        focusTarget: focusTarget,\n        blur: blur,\n        focus: focus,\n        focusDayAfter: function () { return moveFocus('day', 'after'); },\n        focusDayBefore: function () { return moveFocus('day', 'before'); },\n        focusWeekAfter: function () { return moveFocus('week', 'after'); },\n        focusWeekBefore: function () { return moveFocus('week', 'before'); },\n        focusMonthBefore: function () { return moveFocus('month', 'before'); },\n        focusMonthAfter: function () { return moveFocus('month', 'after'); },\n        focusYearBefore: function () { return moveFocus('year', 'before'); },\n        focusYearAfter: function () { return moveFocus('year', 'after'); },\n        focusStartOfWeek: function () { return moveFocus('startOfWeek', 'before'); },\n        focusEndOfWeek: function () { return moveFocus('endOfWeek', 'after'); }\n    };\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(FocusContext.Provider, { value: value, children: props.children }));\n}\n/**\n * Hook to access the {@link FocusContextValue}. Use this hook to handle the\n * focus state of the elements.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useFocusContext() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(FocusContext);\n    if (!context) {\n        throw new Error('useFocusContext must be used within a FocusProvider');\n    }\n    return context;\n}\n\n/**\n * Return the active modifiers for the specified day.\n *\n * This hook is meant to be used inside internal or custom components.\n *\n * @param day\n * @param displayMonth\n */\nfunction useActiveModifiers(day, \n/**\n * The month where the date is displayed. If not the same as `date`, the day\n * is an \"outside day\".\n */\ndisplayMonth) {\n    var modifiers = useModifiers();\n    var activeModifiers = getActiveModifiers(day, modifiers, displayMonth);\n    return activeModifiers;\n}\n\n/**\n * The SelectSingle context shares details about the selected days when in\n * single selection mode.\n *\n * Access this context from the {@link useSelectSingle} hook.\n */\nvar SelectSingleContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/** Provides the values for the {@link SelectSingleProvider}. */\nfunction SelectSingleProvider(props) {\n    if (!isDayPickerSingle(props.initialProps)) {\n        var emptyContextValue = {\n            selected: undefined\n        };\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectSingleContext.Provider, { value: emptyContextValue, children: props.children }));\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectSingleProviderInternal, { initialProps: props.initialProps, children: props.children }));\n}\nfunction SelectSingleProviderInternal(_a) {\n    var initialProps = _a.initialProps, children = _a.children;\n    var onDayClick = function (day, activeModifiers, e) {\n        var _a, _b, _c;\n        (_a = initialProps.onDayClick) === null || _a === void 0 ? void 0 : _a.call(initialProps, day, activeModifiers, e);\n        if (activeModifiers.selected && !initialProps.required) {\n            (_b = initialProps.onSelect) === null || _b === void 0 ? void 0 : _b.call(initialProps, undefined, day, activeModifiers, e);\n            return;\n        }\n        (_c = initialProps.onSelect) === null || _c === void 0 ? void 0 : _c.call(initialProps, day, day, activeModifiers, e);\n    };\n    var contextValue = {\n        selected: initialProps.selected,\n        onDayClick: onDayClick\n    };\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectSingleContext.Provider, { value: contextValue, children: children }));\n}\n/**\n * Hook to access the {@link SelectSingleContextValue}.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useSelectSingle() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SelectSingleContext);\n    if (!context) {\n        throw new Error('useSelectSingle must be used within a SelectSingleProvider');\n    }\n    return context;\n}\n\n/**\n * This hook returns details about the content to render in the day cell.\n *\n *\n * When a day cell is rendered in the table, DayPicker can either:\n *\n * - render nothing: when the day is outside the month or has matched the\n *   \"hidden\" modifier.\n * - render a button when `onDayClick` or a selection mode is set.\n * - render a non-interactive element: when no selection mode is set, the day\n *   cell shouldn’t respond to any interaction. DayPicker should render a `div`\n *   or a `span`.\n *\n * ### Usage\n *\n * Use this hook to customize the behavior of the {@link Day} component. Create a\n * new `Day` component using this hook and pass it to the `components` prop.\n * The source of {@link Day} can be a good starting point.\n *\n */\nfunction useDayEventHandlers(date, activeModifiers) {\n    var dayPicker = useDayPicker();\n    var single = useSelectSingle();\n    var multiple = useSelectMultiple();\n    var range = useSelectRange();\n    var _a = useFocusContext(), focusDayAfter = _a.focusDayAfter, focusDayBefore = _a.focusDayBefore, focusWeekAfter = _a.focusWeekAfter, focusWeekBefore = _a.focusWeekBefore, blur = _a.blur, focus = _a.focus, focusMonthBefore = _a.focusMonthBefore, focusMonthAfter = _a.focusMonthAfter, focusYearBefore = _a.focusYearBefore, focusYearAfter = _a.focusYearAfter, focusStartOfWeek = _a.focusStartOfWeek, focusEndOfWeek = _a.focusEndOfWeek;\n    var onClick = function (e) {\n        var _a, _b, _c, _d;\n        if (isDayPickerSingle(dayPicker)) {\n            (_a = single.onDayClick) === null || _a === void 0 ? void 0 : _a.call(single, date, activeModifiers, e);\n        }\n        else if (isDayPickerMultiple(dayPicker)) {\n            (_b = multiple.onDayClick) === null || _b === void 0 ? void 0 : _b.call(multiple, date, activeModifiers, e);\n        }\n        else if (isDayPickerRange(dayPicker)) {\n            (_c = range.onDayClick) === null || _c === void 0 ? void 0 : _c.call(range, date, activeModifiers, e);\n        }\n        else {\n            (_d = dayPicker.onDayClick) === null || _d === void 0 ? void 0 : _d.call(dayPicker, date, activeModifiers, e);\n        }\n    };\n    var onFocus = function (e) {\n        var _a;\n        focus(date);\n        (_a = dayPicker.onDayFocus) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onBlur = function (e) {\n        var _a;\n        blur();\n        (_a = dayPicker.onDayBlur) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onMouseEnter = function (e) {\n        var _a;\n        (_a = dayPicker.onDayMouseEnter) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onMouseLeave = function (e) {\n        var _a;\n        (_a = dayPicker.onDayMouseLeave) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onPointerEnter = function (e) {\n        var _a;\n        (_a = dayPicker.onDayPointerEnter) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onPointerLeave = function (e) {\n        var _a;\n        (_a = dayPicker.onDayPointerLeave) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onTouchCancel = function (e) {\n        var _a;\n        (_a = dayPicker.onDayTouchCancel) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onTouchEnd = function (e) {\n        var _a;\n        (_a = dayPicker.onDayTouchEnd) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onTouchMove = function (e) {\n        var _a;\n        (_a = dayPicker.onDayTouchMove) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onTouchStart = function (e) {\n        var _a;\n        (_a = dayPicker.onDayTouchStart) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onKeyUp = function (e) {\n        var _a;\n        (_a = dayPicker.onDayKeyUp) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onKeyDown = function (e) {\n        var _a;\n        switch (e.key) {\n            case 'ArrowLeft':\n                e.preventDefault();\n                e.stopPropagation();\n                dayPicker.dir === 'rtl' ? focusDayAfter() : focusDayBefore();\n                break;\n            case 'ArrowRight':\n                e.preventDefault();\n                e.stopPropagation();\n                dayPicker.dir === 'rtl' ? focusDayBefore() : focusDayAfter();\n                break;\n            case 'ArrowDown':\n                e.preventDefault();\n                e.stopPropagation();\n                focusWeekAfter();\n                break;\n            case 'ArrowUp':\n                e.preventDefault();\n                e.stopPropagation();\n                focusWeekBefore();\n                break;\n            case 'PageUp':\n                e.preventDefault();\n                e.stopPropagation();\n                e.shiftKey ? focusYearBefore() : focusMonthBefore();\n                break;\n            case 'PageDown':\n                e.preventDefault();\n                e.stopPropagation();\n                e.shiftKey ? focusYearAfter() : focusMonthAfter();\n                break;\n            case 'Home':\n                e.preventDefault();\n                e.stopPropagation();\n                focusStartOfWeek();\n                break;\n            case 'End':\n                e.preventDefault();\n                e.stopPropagation();\n                focusEndOfWeek();\n                break;\n        }\n        (_a = dayPicker.onDayKeyDown) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var eventHandlers = {\n        onClick: onClick,\n        onFocus: onFocus,\n        onBlur: onBlur,\n        onKeyDown: onKeyDown,\n        onKeyUp: onKeyUp,\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave,\n        onPointerEnter: onPointerEnter,\n        onPointerLeave: onPointerLeave,\n        onTouchCancel: onTouchCancel,\n        onTouchEnd: onTouchEnd,\n        onTouchMove: onTouchMove,\n        onTouchStart: onTouchStart\n    };\n    return eventHandlers;\n}\n\n/**\n * Return the current selected days when DayPicker is in selection mode. Days\n * selected by the custom selection mode are not returned.\n *\n * This hook is meant to be used inside internal or custom components.\n *\n */\nfunction useSelectedDays() {\n    var dayPicker = useDayPicker();\n    var single = useSelectSingle();\n    var multiple = useSelectMultiple();\n    var range = useSelectRange();\n    var selectedDays = isDayPickerSingle(dayPicker)\n        ? single.selected\n        : isDayPickerMultiple(dayPicker)\n            ? multiple.selected\n            : isDayPickerRange(dayPicker)\n                ? range.selected\n                : undefined;\n    return selectedDays;\n}\n\nfunction isInternalModifier(modifier) {\n    return Object.values(InternalModifier).includes(modifier);\n}\n/**\n * Return the class names for the Day element, according to the given active\n * modifiers.\n *\n * Custom class names are set via `modifiersClassNames` or `classNames`,\n * where the first have the precedence.\n */\nfunction getDayClassNames(dayPicker, activeModifiers) {\n    var classNames = [dayPicker.classNames.day];\n    Object.keys(activeModifiers).forEach(function (modifier) {\n        var customClassName = dayPicker.modifiersClassNames[modifier];\n        if (customClassName) {\n            classNames.push(customClassName);\n        }\n        else if (isInternalModifier(modifier)) {\n            var internalClassName = dayPicker.classNames[\"day_\".concat(modifier)];\n            if (internalClassName) {\n                classNames.push(internalClassName);\n            }\n        }\n    });\n    return classNames;\n}\n\n/** Return the style for the Day element, according to the given active modifiers. */\nfunction getDayStyle(dayPicker, activeModifiers) {\n    var style = __assign({}, dayPicker.styles.day);\n    Object.keys(activeModifiers).forEach(function (modifier) {\n        var _a;\n        style = __assign(__assign({}, style), (_a = dayPicker.modifiersStyles) === null || _a === void 0 ? void 0 : _a[modifier]);\n    });\n    return style;\n}\n\n/**\n * Return props and data used to render the {@link Day} component.\n *\n * Use this hook when creating a component to replace the built-in `Day`\n * component.\n */\nfunction useDayRender(\n/** The date to render. */\nday, \n/** The month where the date is displayed (if not the same as `date`, it means it is an \"outside\" day). */\ndisplayMonth, \n/** A ref to the button element that will be target of focus when rendered (if required). */\nbuttonRef) {\n    var _a;\n    var _b, _c;\n    var dayPicker = useDayPicker();\n    var focusContext = useFocusContext();\n    var activeModifiers = useActiveModifiers(day, displayMonth);\n    var eventHandlers = useDayEventHandlers(day, activeModifiers);\n    var selectedDays = useSelectedDays();\n    var isButton = Boolean(dayPicker.onDayClick || dayPicker.mode !== 'default');\n    // Focus the button if the day is focused according to the focus context\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n        var _a;\n        if (activeModifiers.outside)\n            return;\n        if (!focusContext.focusedDay)\n            return;\n        if (!isButton)\n            return;\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(focusContext.focusedDay, day)) {\n            (_a = buttonRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n        }\n    }, [\n        focusContext.focusedDay,\n        day,\n        buttonRef,\n        isButton,\n        activeModifiers.outside\n    ]);\n    var className = getDayClassNames(dayPicker, activeModifiers).join(' ');\n    var style = getDayStyle(dayPicker, activeModifiers);\n    var isHidden = Boolean((activeModifiers.outside && !dayPicker.showOutsideDays) ||\n        activeModifiers.hidden);\n    var DayContentComponent = (_c = (_b = dayPicker.components) === null || _b === void 0 ? void 0 : _b.DayContent) !== null && _c !== void 0 ? _c : DayContent;\n    var children = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DayContentComponent, { date: day, displayMonth: displayMonth, activeModifiers: activeModifiers }));\n    var divProps = {\n        style: style,\n        className: className,\n        children: children,\n        role: 'gridcell'\n    };\n    var isFocusTarget = focusContext.focusTarget &&\n        (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(focusContext.focusTarget, day) &&\n        !activeModifiers.outside;\n    var isFocused = focusContext.focusedDay && (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(focusContext.focusedDay, day);\n    var buttonProps = __assign(__assign(__assign({}, divProps), (_a = { disabled: activeModifiers.disabled, role: 'gridcell' }, _a['aria-selected'] = activeModifiers.selected, _a.tabIndex = isFocused || isFocusTarget ? 0 : -1, _a)), eventHandlers);\n    var dayRender = {\n        isButton: isButton,\n        isHidden: isHidden,\n        activeModifiers: activeModifiers,\n        selectedDays: selectedDays,\n        buttonProps: buttonProps,\n        divProps: divProps\n    };\n    return dayRender;\n}\n\n/**\n * The content of a day cell – as a button or span element according to its\n * modifiers.\n */\nfunction Day(props) {\n    var buttonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var dayRender = useDayRender(props.date, props.displayMonth, buttonRef);\n    if (dayRender.isHidden) {\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { role: \"gridcell\" });\n    }\n    if (!dayRender.isButton) {\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", __assign({}, dayRender.divProps));\n    }\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Button, __assign({ name: \"day\", ref: buttonRef }, dayRender.buttonProps));\n}\n\n/**\n * Render the week number element. If `onWeekNumberClick` is passed to DayPicker, it\n * renders a button, otherwise a span element.\n */\nfunction WeekNumber(props) {\n    var weekNumber = props.number, dates = props.dates;\n    var _a = useDayPicker(), onWeekNumberClick = _a.onWeekNumberClick, styles = _a.styles, classNames = _a.classNames, locale = _a.locale, labelWeekNumber = _a.labels.labelWeekNumber, formatWeekNumber = _a.formatters.formatWeekNumber;\n    var content = formatWeekNumber(Number(weekNumber), { locale: locale });\n    if (!onWeekNumberClick) {\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", { className: classNames.weeknumber, style: styles.weeknumber, children: content }));\n    }\n    var label = labelWeekNumber(Number(weekNumber), { locale: locale });\n    var handleClick = function (e) {\n        onWeekNumberClick(weekNumber, dates, e);\n    };\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Button, { name: \"week-number\", \"aria-label\": label, className: classNames.weeknumber, style: styles.weeknumber, onClick: handleClick, children: content }));\n}\n\n/** Render a row in the calendar, with the days and the week number. */\nfunction Row(props) {\n    var _a, _b;\n    var _c = useDayPicker(), styles = _c.styles, classNames = _c.classNames, showWeekNumber = _c.showWeekNumber, components = _c.components;\n    var DayComponent = (_a = components === null || components === void 0 ? void 0 : components.Day) !== null && _a !== void 0 ? _a : Day;\n    var WeeknumberComponent = (_b = components === null || components === void 0 ? void 0 : components.WeekNumber) !== null && _b !== void 0 ? _b : WeekNumber;\n    var weekNumberCell;\n    if (showWeekNumber) {\n        weekNumberCell = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { className: classNames.cell, style: styles.cell, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(WeeknumberComponent, { number: props.weekNumber, dates: props.dates }) }));\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"tr\", { className: classNames.row, style: styles.row, children: [weekNumberCell, props.dates.map(function (date) { return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { className: classNames.cell, style: styles.cell, role: \"presentation\", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DayComponent, { displayMonth: props.displayMonth, date: date }) }, (0,date_fns__WEBPACK_IMPORTED_MODULE_29__.getUnixTime)(date))); })] }));\n}\n\n/** Return the weeks between two dates.  */\nfunction daysToMonthWeeks(fromDate, toDate, options) {\n    var toWeek = (options === null || options === void 0 ? void 0 : options.ISOWeek)\n        ? (0,date_fns__WEBPACK_IMPORTED_MODULE_25__.endOfISOWeek)(toDate)\n        : (0,date_fns__WEBPACK_IMPORTED_MODULE_26__.endOfWeek)(toDate, options);\n    var fromWeek = (options === null || options === void 0 ? void 0 : options.ISOWeek)\n        ? (0,date_fns__WEBPACK_IMPORTED_MODULE_15__.startOfISOWeek)(fromDate)\n        : (0,date_fns__WEBPACK_IMPORTED_MODULE_16__.startOfWeek)(fromDate, options);\n    var nOfDays = (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(toWeek, fromWeek);\n    var days = [];\n    for (var i = 0; i <= nOfDays; i++) {\n        days.push((0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(fromWeek, i));\n    }\n    var weeksInMonth = days.reduce(function (result, date) {\n        var weekNumber = (options === null || options === void 0 ? void 0 : options.ISOWeek)\n            ? (0,date_fns__WEBPACK_IMPORTED_MODULE_30__.getISOWeek)(date)\n            : (0,date_fns__WEBPACK_IMPORTED_MODULE_31__.getWeek)(date, options);\n        var existingWeek = result.find(function (value) { return value.weekNumber === weekNumber; });\n        if (existingWeek) {\n            existingWeek.dates.push(date);\n            return result;\n        }\n        result.push({\n            weekNumber: weekNumber,\n            dates: [date]\n        });\n        return result;\n    }, []);\n    return weeksInMonth;\n}\n\n/**\n * Return the weeks belonging to the given month, adding the \"outside days\" to\n * the first and last week.\n */\nfunction getMonthWeeks(month, options) {\n    var weeksInMonth = daysToMonthWeeks((0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(month), (0,date_fns__WEBPACK_IMPORTED_MODULE_5__.endOfMonth)(month), options);\n    if (options === null || options === void 0 ? void 0 : options.useFixedWeeks) {\n        // Add extra weeks to the month, up to 6 weeks\n        var nrOfMonthWeeks = (0,date_fns__WEBPACK_IMPORTED_MODULE_32__.getWeeksInMonth)(month, options);\n        if (nrOfMonthWeeks < 6) {\n            var lastWeek = weeksInMonth[weeksInMonth.length - 1];\n            var lastDate = lastWeek.dates[lastWeek.dates.length - 1];\n            var toDate = (0,date_fns__WEBPACK_IMPORTED_MODULE_23__.addWeeks)(lastDate, 6 - nrOfMonthWeeks);\n            var extraWeeks = daysToMonthWeeks((0,date_fns__WEBPACK_IMPORTED_MODULE_23__.addWeeks)(lastDate, 1), toDate, options);\n            weeksInMonth.push.apply(weeksInMonth, extraWeeks);\n        }\n    }\n    return weeksInMonth;\n}\n\n/** Render the table with the calendar. */\nfunction Table(props) {\n    var _a, _b, _c;\n    var _d = useDayPicker(), locale = _d.locale, classNames = _d.classNames, styles = _d.styles, hideHead = _d.hideHead, fixedWeeks = _d.fixedWeeks, components = _d.components, weekStartsOn = _d.weekStartsOn, firstWeekContainsDate = _d.firstWeekContainsDate, ISOWeek = _d.ISOWeek;\n    var weeks = getMonthWeeks(props.displayMonth, {\n        useFixedWeeks: Boolean(fixedWeeks),\n        ISOWeek: ISOWeek,\n        locale: locale,\n        weekStartsOn: weekStartsOn,\n        firstWeekContainsDate: firstWeekContainsDate\n    });\n    var HeadComponent = (_a = components === null || components === void 0 ? void 0 : components.Head) !== null && _a !== void 0 ? _a : Head;\n    var RowComponent = (_b = components === null || components === void 0 ? void 0 : components.Row) !== null && _b !== void 0 ? _b : Row;\n    var FooterComponent = (_c = components === null || components === void 0 ? void 0 : components.Footer) !== null && _c !== void 0 ? _c : Footer;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"table\", { id: props.id, className: classNames.table, style: styles.table, role: \"grid\", \"aria-labelledby\": props['aria-labelledby'], children: [!hideHead && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(HeadComponent, {}), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"tbody\", { className: classNames.tbody, style: styles.tbody, children: weeks.map(function (week) { return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(RowComponent, { displayMonth: props.displayMonth, dates: week.dates, weekNumber: week.weekNumber }, week.weekNumber)); }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(FooterComponent, { displayMonth: props.displayMonth })] }));\n}\n\n/*\nThe MIT License (MIT)\n\nCopyright (c) 2018-present, React Training LLC\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n/* eslint-disable prefer-const */\n/* eslint-disable @typescript-eslint/ban-ts-comment */\n/*\n * Welcome to @reach/auto-id!\n * Let's see if we can make sense of why this hook exists and its\n * implementation.\n *\n * Some background:\n *   1. Accessibility APIs rely heavily on element IDs\n *   2. Requiring developers to put IDs on every element in Reach UI is both\n *      cumbersome and error-prone\n *   3. With a component model, we can generate IDs for them!\n *\n * Solution 1: Generate random IDs.\n *\n * This works great as long as you don't server render your app. When React (in\n * the client) tries to reuse the markup from the server, the IDs won't match\n * and React will then recreate the entire DOM tree.\n *\n * Solution 2: Increment an integer\n *\n * This sounds great. Since we're rendering the exact same tree on the server\n * and client, we can increment a counter and get a deterministic result between\n * client and server. Also, JS integers can go up to nine-quadrillion. I'm\n * pretty sure the tab will be closed before an app never needs\n * 10 quadrillion IDs!\n *\n * Problem solved, right?\n *\n * Ah, but there's a catch! React's concurrent rendering makes this approach\n * non-deterministic. While the client and server will end up with the same\n * elements in the end, depending on suspense boundaries (and possibly some user\n * input during the initial render) the incrementing integers won't always match\n * up.\n *\n * Solution 3: Don't use IDs at all on the server; patch after first render.\n *\n * What we've done here is solution 2 with some tricks. With this approach, the\n * ID returned is an empty string on the first render. This way the server and\n * client have the same markup no matter how wild the concurrent rendering may\n * have gotten.\n *\n * After the render, we patch up the components with an incremented ID. This\n * causes a double render on any components with `useId`. Shouldn't be a problem\n * since the components using this hook should be small, and we're only updating\n * the ID attribute on the DOM, nothing big is happening.\n *\n * It doesn't have to be an incremented number, though--we could do generate\n * random strings instead, but incrementing a number is probably the cheapest\n * thing we can do.\n *\n * Additionally, we only do this patchup on the very first client render ever.\n * Any calls to `useId` that happen dynamically in the client will be\n * populated immediately with a value. So, we only get the double render after\n * server hydration and never again, SO BACK OFF ALRIGHT?\n */\nfunction canUseDOM() {\n    return !!(typeof window !== 'undefined' &&\n        window.document &&\n        window.document.createElement);\n}\n/**\n * React currently throws a warning when using useLayoutEffect on the server. To\n * get around it, we can conditionally useEffect on the server (no-op) and\n * useLayoutEffect in the browser. We occasionally need useLayoutEffect to\n * ensure we don't get a render flash for certain operations, but we may also\n * need affected components to render on the server. One example is when setting\n * a component's descendants to retrieve their index values.\n *\n * Important to note that using this hook as an escape hatch will break the\n * eslint dependency warnings unless you rename the import to `useLayoutEffect`.\n * Use sparingly only when the effect won't effect the rendered HTML to avoid\n * any server/client mismatch.\n *\n * If a useLayoutEffect is needed and the result would create a mismatch, it's\n * likely that the component in question shouldn't be rendered on the server at\n * all, so a better approach would be to lazily render those in a parent\n * component after client-side hydration.\n *\n * https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85\n * https://github.com/reduxjs/react-redux/blob/master/src/utils/useIsomorphicLayoutEffect.js\n *\n * @param effect\n * @param deps\n */\nvar useIsomorphicLayoutEffect = canUseDOM() ? react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_1__.useEffect;\nvar serverHandoffComplete = false;\nvar id = 0;\nfunction genId() {\n    return \"react-day-picker-\".concat(++id);\n}\nfunction useId(providedId) {\n    // TODO: Remove error flag when updating internal deps to React 18. None of\n    // our tricks will play well with concurrent rendering anyway.\n    var _a;\n    // If this instance isn't part of the initial render, we don't have to do the\n    // double render/patch-up dance. We can just generate the ID and return it.\n    var initialId = providedId !== null && providedId !== void 0 ? providedId : (serverHandoffComplete ? genId() : null);\n    var _b = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialId), id = _b[0], setId = _b[1];\n    useIsomorphicLayoutEffect(function () {\n        if (id === null) {\n            // Patch the ID after render. We do this in `useLayoutEffect` to avoid any\n            // rendering flicker, though it'll make the first render slower (unlikely\n            // to matter, but you're welcome to measure your app and let us know if\n            // it's a problem).\n            setId(genId());\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n        if (serverHandoffComplete === false) {\n            // Flag all future uses of `useId` to skip the update dance. This is in\n            // `useEffect` because it goes after `useLayoutEffect`, ensuring we don't\n            // accidentally bail out of the patch-up dance prematurely.\n            serverHandoffComplete = true;\n        }\n    }, []);\n    return (_a = providedId !== null && providedId !== void 0 ? providedId : id) !== null && _a !== void 0 ? _a : undefined;\n}\n\n/** Render a month. */\nfunction Month(props) {\n    var _a;\n    var _b;\n    var dayPicker = useDayPicker();\n    var dir = dayPicker.dir, classNames = dayPicker.classNames, styles = dayPicker.styles, components = dayPicker.components;\n    var displayMonths = useNavigation().displayMonths;\n    var captionId = useId(dayPicker.id ? \"\".concat(dayPicker.id, \"-\").concat(props.displayIndex) : undefined);\n    var tableId = dayPicker.id\n        ? \"\".concat(dayPicker.id, \"-grid-\").concat(props.displayIndex)\n        : undefined;\n    var className = [classNames.month];\n    var style = styles.month;\n    var isStart = props.displayIndex === 0;\n    var isEnd = props.displayIndex === displayMonths.length - 1;\n    var isCenter = !isStart && !isEnd;\n    if (dir === 'rtl') {\n        _a = [isStart, isEnd], isEnd = _a[0], isStart = _a[1];\n    }\n    if (isStart) {\n        className.push(classNames.caption_start);\n        style = __assign(__assign({}, style), styles.caption_start);\n    }\n    if (isEnd) {\n        className.push(classNames.caption_end);\n        style = __assign(__assign({}, style), styles.caption_end);\n    }\n    if (isCenter) {\n        className.push(classNames.caption_between);\n        style = __assign(__assign({}, style), styles.caption_between);\n    }\n    var CaptionComponent = (_b = components === null || components === void 0 ? void 0 : components.Caption) !== null && _b !== void 0 ? _b : Caption;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { className: className.join(' '), style: style, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionComponent, { id: captionId, displayMonth: props.displayMonth, displayIndex: props.displayIndex }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Table, { id: tableId, \"aria-labelledby\": captionId, displayMonth: props.displayMonth })] }, props.displayIndex));\n}\n\n/**\n * Render the wrapper for the month grids.\n */\nfunction Months(props) {\n    var _a = useDayPicker(), classNames = _a.classNames, styles = _a.styles;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: classNames.months, style: styles.months, children: props.children }));\n}\n\n/** Render the container with the months according to the number of months to display. */\nfunction Root(_a) {\n    var _b, _c;\n    var initialProps = _a.initialProps;\n    var dayPicker = useDayPicker();\n    var focusContext = useFocusContext();\n    var navigation = useNavigation();\n    var _d = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), hasInitialFocus = _d[0], setHasInitialFocus = _d[1];\n    // Focus the focus target when initialFocus is passed in\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n        if (!dayPicker.initialFocus)\n            return;\n        if (!focusContext.focusTarget)\n            return;\n        if (hasInitialFocus)\n            return;\n        focusContext.focus(focusContext.focusTarget);\n        setHasInitialFocus(true);\n    }, [\n        dayPicker.initialFocus,\n        hasInitialFocus,\n        focusContext.focus,\n        focusContext.focusTarget,\n        focusContext\n    ]);\n    // Apply classnames according to props\n    var classNames = [dayPicker.classNames.root, dayPicker.className];\n    if (dayPicker.numberOfMonths > 1) {\n        classNames.push(dayPicker.classNames.multiple_months);\n    }\n    if (dayPicker.showWeekNumber) {\n        classNames.push(dayPicker.classNames.with_weeknumber);\n    }\n    var style = __assign(__assign({}, dayPicker.styles.root), dayPicker.style);\n    var dataAttributes = Object.keys(initialProps)\n        .filter(function (key) { return key.startsWith('data-'); })\n        .reduce(function (attrs, key) {\n        var _a;\n        return __assign(__assign({}, attrs), (_a = {}, _a[key] = initialProps[key], _a));\n    }, {});\n    var MonthsComponent = (_c = (_b = initialProps.components) === null || _b === void 0 ? void 0 : _b.Months) !== null && _c !== void 0 ? _c : Months;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", __assign({ className: classNames.join(' '), style: style, dir: dayPicker.dir, id: dayPicker.id, nonce: initialProps.nonce, title: initialProps.title, lang: initialProps.lang }, dataAttributes, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(MonthsComponent, { children: navigation.displayMonths.map(function (month, i) { return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Month, { displayIndex: i, displayMonth: month }, i)); }) }) })));\n}\n\n/** Provide the value for all the context providers. */\nfunction RootProvider(props) {\n    var children = props.children, initialProps = __rest(props, [\"children\"]);\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DayPickerProvider, { initialProps: initialProps, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(NavigationProvider, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectSingleProvider, { initialProps: initialProps, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectMultipleProvider, { initialProps: initialProps, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectRangeProvider, { initialProps: initialProps, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ModifiersProvider, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(FocusProvider, { children: children }) }) }) }) }) }) }));\n}\n\n/**\n * DayPicker render a date picker component to let users pick dates from a\n * calendar. See http://react-day-picker.js.org for updated documentation and\n * examples.\n *\n * ### Customization\n *\n * DayPicker offers different customization props. For example,\n *\n * - show multiple months using `numberOfMonths`\n * - display a dropdown to navigate the months via `captionLayout`\n * - display the week numbers with `showWeekNumbers`\n * - disable or hide days with `disabled` or `hidden`\n *\n * ### Controlling the months\n *\n * Change the initially displayed month using the `defaultMonth` prop. The\n * displayed months are controlled by DayPicker and stored in its internal\n * state. To control the months yourself, use `month` instead of `defaultMonth`\n * and use the `onMonthChange` event to set it.\n *\n * To limit the months the user can navigate to, use\n * `fromDate`/`fromMonth`/`fromYear` or `toDate`/`toMonth`/`toYear`.\n *\n * ### Selection modes\n *\n * DayPicker supports different selection mode that can be toggled using the\n * `mode` prop:\n *\n * - `mode=\"single\"`: only one day can be selected. Use `required` to make the\n *   selection required. Use the `onSelect` event handler to get the selected\n *   days.\n * - `mode=\"multiple\"`: users can select one or more days. Limit the amount of\n *   days that can be selected with the `min` or the `max` props.\n * - `mode=\"range\"`: users can select a range of days. Limit the amount of days\n *   in the range with the `min` or the `max` props.\n * - `mode=\"default\"` (default): the built-in selections are disabled. Implement\n *   your own selection mode with `onDayClick`.\n *\n * The selection modes should cover the most common use cases. In case you\n * need a more refined way of selecting days, use `mode=\"default\"`. Use the\n * `selected` props and add the day event handlers to add/remove days from the\n * selection.\n *\n * ### Modifiers\n *\n * A _modifier_ represents different styles or states for the days displayed in\n * the calendar (like \"selected\" or \"disabled\"). Define custom modifiers using\n * the `modifiers` prop.\n *\n * ### Formatters and custom component\n *\n * You can customize how the content is displayed in the date picker by using\n * either the formatters or replacing the internal components.\n *\n * For the most common cases you want to use the `formatters` prop to change how\n * the content is formatted in the calendar. Use the `components` prop to\n * replace the internal components, like the navigation icons.\n *\n * ### Styling\n *\n * DayPicker comes with a default, basic style in `react-day-picker/style` – use\n * it as template for your own style.\n *\n * If you are using CSS modules, pass the imported styles object the\n * `classNames` props.\n *\n * You can also style the elements via inline styles using the `styles` prop.\n *\n * ### Form fields\n *\n * If you need to bind the date picker to a form field, you can use the\n * `useInput` hooks for a basic behavior. See the `useInput` source as an\n * example to bind the date picker with form fields.\n *\n * ### Localization\n *\n * To localize DayPicker, import the locale from `date-fns` package and use the\n * `locale` prop.\n *\n * For example, to use Spanish locale:\n *\n * ```\n * import { es } from 'date-fns/locale';\n * <DayPicker locale={es} />\n * ```\n */\nfunction DayPicker(props) {\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(RootProvider, __assign({}, props, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Root, { initialProps: props }) })));\n}\n\n/** @private */\nfunction isValidDate(day) {\n    return !isNaN(day.getTime());\n}\n\n/** Return props and setters for binding an input field to DayPicker. */\nfunction useInput(options) {\n    if (options === void 0) { options = {}; }\n    var _a = options.locale, locale = _a === void 0 ? date_fns_locale__WEBPACK_IMPORTED_MODULE_3__.enUS : _a, required = options.required, _b = options.format, format$1 = _b === void 0 ? 'PP' : _b, defaultSelected = options.defaultSelected, _c = options.today, today = _c === void 0 ? new Date() : _c;\n    var _d = parseFromToProps(options), fromDate = _d.fromDate, toDate = _d.toDate;\n    // Shortcut to the DateFns functions\n    var parseValue = function (value) { return (0,date_fns__WEBPACK_IMPORTED_MODULE_33__.parse)(value, format$1, today, { locale: locale }); };\n    // Initialize states\n    var _e = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultSelected !== null && defaultSelected !== void 0 ? defaultSelected : today), month = _e[0], setMonth = _e[1];\n    var _f = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultSelected), selectedDay = _f[0], setSelectedDay = _f[1];\n    var defaultInputValue = defaultSelected\n        ? (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(defaultSelected, format$1, { locale: locale })\n        : '';\n    var _g = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultInputValue), inputValue = _g[0], setInputValue = _g[1];\n    var reset = function () {\n        setSelectedDay(defaultSelected);\n        setMonth(defaultSelected !== null && defaultSelected !== void 0 ? defaultSelected : today);\n        setInputValue(defaultInputValue !== null && defaultInputValue !== void 0 ? defaultInputValue : '');\n    };\n    var setSelected = function (date) {\n        setSelectedDay(date);\n        setMonth(date !== null && date !== void 0 ? date : today);\n        setInputValue(date ? (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(date, format$1, { locale: locale }) : '');\n    };\n    var handleDayClick = function (day, _a) {\n        var selected = _a.selected;\n        if (!required && selected) {\n            setSelectedDay(undefined);\n            setInputValue('');\n            return;\n        }\n        setSelectedDay(day);\n        setInputValue(day ? (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(day, format$1, { locale: locale }) : '');\n    };\n    var handleMonthChange = function (month) {\n        setMonth(month);\n    };\n    // When changing the input field, save its value in state and check if the\n    // string is a valid date. If it is a valid day, set it as selected and update\n    // the calendar’s month.\n    var handleChange = function (e) {\n        setInputValue(e.target.value);\n        var day = parseValue(e.target.value);\n        var isBefore = fromDate && (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(fromDate, day) > 0;\n        var isAfter = toDate && (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(day, toDate) > 0;\n        if (!isValidDate(day) || isBefore || isAfter) {\n            setSelectedDay(undefined);\n            return;\n        }\n        setSelectedDay(day);\n        setMonth(day);\n    };\n    // Special case for _required_ fields: on blur, if the value of the input is not\n    // a valid date, reset the calendar and the input value.\n    var handleBlur = function (e) {\n        var day = parseValue(e.target.value);\n        if (!isValidDate(day)) {\n            reset();\n        }\n    };\n    // When focusing, make sure DayPicker visualizes the month of the date in the\n    // input field.\n    var handleFocus = function (e) {\n        if (!e.target.value) {\n            reset();\n            return;\n        }\n        var day = parseValue(e.target.value);\n        if (isValidDate(day)) {\n            setMonth(day);\n        }\n    };\n    var dayPickerProps = {\n        month: month,\n        onDayClick: handleDayClick,\n        onMonthChange: handleMonthChange,\n        selected: selectedDay,\n        locale: locale,\n        fromDate: fromDate,\n        toDate: toDate,\n        today: today\n    };\n    var inputProps = {\n        onBlur: handleBlur,\n        onChange: handleChange,\n        onFocus: handleFocus,\n        value: inputValue,\n        placeholder: (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(new Date(), format$1, { locale: locale })\n    };\n    return { dayPickerProps: dayPickerProps, inputProps: inputProps, reset: reset, setSelected: setSelected };\n}\n\n/** Returns true when the props are of type {@link DayPickerDefaultProps}. */\nfunction isDayPickerDefault(props) {\n    return props.mode === undefined || props.mode === 'default';\n}\n\n\n//# sourceMappingURL=index.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-day-picker/dist/index.esm.js\n");

/***/ })

};
;