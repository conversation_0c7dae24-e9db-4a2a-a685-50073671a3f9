"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/websocket-driver";
exports.ids = ["vendor-chunks/websocket-driver"];
exports.modules = {

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver.js":
/*!***************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// Protocol references:\n//\n// * http://tools.ietf.org/html/draft-hixie-thewebsocketprotocol-75\n// * http://tools.ietf.org/html/draft-hixie-thewebsocketprotocol-76\n// * http://tools.ietf.org/html/draft-ietf-hybi-thewebsocketprotocol-17\n\nvar Base   = __webpack_require__(/*! ./driver/base */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/base.js\"),\n    Client = __webpack_require__(/*! ./driver/client */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/client.js\"),\n    Server = __webpack_require__(/*! ./driver/server */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/server.js\");\n\nvar Driver = {\n  client: function(url, options) {\n    options = options || {};\n    if (options.masking === undefined) options.masking = true;\n    return new Client(url, options);\n  },\n\n  server: function(options) {\n    options = options || {};\n    if (options.requireMasking === undefined) options.requireMasking = true;\n    return new Server(options);\n  },\n\n  http: function() {\n    return Server.http.apply(Server, arguments);\n  },\n\n  isSecureRequest: function(request) {\n    return Server.isSecureRequest(request);\n  },\n\n  isWebSocket: function(request) {\n    return Base.isWebSocket(request);\n  },\n\n  validateOptions: function(options, validKeys) {\n    Base.validateOptions(options, validKeys);\n  }\n};\n\nmodule.exports = Driver;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/base.js":
/*!********************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/base.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Buffer  = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer),\n    Emitter = (__webpack_require__(/*! events */ \"events\").EventEmitter),\n    util    = __webpack_require__(/*! util */ \"util\"),\n    streams = __webpack_require__(/*! ../streams */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/streams.js\"),\n    Headers = __webpack_require__(/*! ./headers */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/headers.js\"),\n    Reader  = __webpack_require__(/*! ./stream_reader */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/stream_reader.js\");\n\nvar Base = function(request, url, options) {\n  Emitter.call(this);\n  Base.validateOptions(options || {}, ['maxLength', 'masking', 'requireMasking', 'protocols']);\n\n  this._request   = request;\n  this._reader    = new Reader();\n  this._options   = options || {};\n  this._maxLength = this._options.maxLength || this.MAX_LENGTH;\n  this._headers   = new Headers();\n  this.__queue    = [];\n  this.readyState = 0;\n  this.url        = url;\n\n  this.io = new streams.IO(this);\n  this.messages = new streams.Messages(this);\n  this._bindEventListeners();\n};\nutil.inherits(Base, Emitter);\n\nBase.isWebSocket = function(request) {\n  var connection = request.headers.connection || '',\n      upgrade    = request.headers.upgrade || '';\n\n  return request.method === 'GET' &&\n         connection.toLowerCase().split(/ *, */).indexOf('upgrade') >= 0 &&\n         upgrade.toLowerCase() === 'websocket';\n};\n\nBase.validateOptions = function(options, validKeys) {\n  for (var key in options) {\n    if (validKeys.indexOf(key) < 0)\n      throw new Error('Unrecognized option: ' + key);\n  }\n};\n\nvar instance = {\n  // This is 64MB, small enough for an average VPS to handle without\n  // crashing from process out of memory\n  MAX_LENGTH: 0x3ffffff,\n\n  STATES: ['connecting', 'open', 'closing', 'closed'],\n\n  _bindEventListeners: function() {\n    var self = this;\n\n    // Protocol errors are informational and do not have to be handled\n    this.messages.on('error', function() {});\n\n    this.on('message', function(event) {\n      var messages = self.messages;\n      if (messages.readable) messages.emit('data', event.data);\n    });\n\n    this.on('error', function(error) {\n      var messages = self.messages;\n      if (messages.readable) messages.emit('error', error);\n    });\n\n    this.on('close', function() {\n      var messages = self.messages;\n      if (!messages.readable) return;\n      messages.readable = messages.writable = false;\n      messages.emit('end');\n    });\n  },\n\n  getState: function() {\n    return this.STATES[this.readyState] || null;\n  },\n\n  addExtension: function(extension) {\n    return false;\n  },\n\n  setHeader: function(name, value) {\n    if (this.readyState > 0) return false;\n    this._headers.set(name, value);\n    return true;\n  },\n\n  start: function() {\n    if (this.readyState !== 0) return false;\n\n    if (!Base.isWebSocket(this._request))\n      return this._failHandshake(new Error('Not a WebSocket request'));\n\n    var response;\n\n    try {\n      response = this._handshakeResponse();\n    } catch (error) {\n      return this._failHandshake(error);\n    }\n\n    this._write(response);\n    if (this._stage !== -1) this._open();\n    return true;\n  },\n\n  _failHandshake: function(error) {\n    var headers = new Headers();\n    headers.set('Content-Type', 'text/plain');\n    headers.set('Content-Length', Buffer.byteLength(error.message, 'utf8'));\n\n    headers = ['HTTP/1.1 400 Bad Request', headers.toString(), error.message];\n    this._write(Buffer.from(headers.join('\\r\\n'), 'utf8'));\n    this._fail('protocol_error', error.message);\n\n    return false;\n  },\n\n  text: function(message) {\n    return this.frame(message);\n  },\n\n  binary: function(message) {\n    return false;\n  },\n\n  ping: function() {\n    return false;\n  },\n\n  pong: function() {\n      return false;\n  },\n\n  close: function(reason, code) {\n    if (this.readyState !== 1) return false;\n    this.readyState = 3;\n    this.emit('close', new Base.CloseEvent(null, null));\n    return true;\n  },\n\n  _open: function() {\n    this.readyState = 1;\n    this.__queue.forEach(function(args) { this.frame.apply(this, args) }, this);\n    this.__queue = [];\n    this.emit('open', new Base.OpenEvent());\n  },\n\n  _queue: function(message) {\n    this.__queue.push(message);\n    return true;\n  },\n\n  _write: function(chunk) {\n    var io = this.io;\n    if (io.readable) io.emit('data', chunk);\n  },\n\n  _fail: function(type, message) {\n    this.readyState = 2;\n    this.emit('error', new Error(message));\n    this.close();\n  }\n};\n\nfor (var key in instance)\n  Base.prototype[key] = instance[key];\n\n\nBase.ConnectEvent = function() {};\n\nBase.OpenEvent = function() {};\n\nBase.CloseEvent = function(code, reason) {\n  this.code   = code;\n  this.reason = reason;\n};\n\nBase.MessageEvent = function(data) {\n  this.data = data;\n};\n\nBase.PingEvent = function(data) {\n  this.data = data;\n};\n\nBase.PongEvent = function(data) {\n  this.data = data;\n};\n\nmodule.exports = Base;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/base.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/client.js":
/*!**********************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/client.js ***!
  \**********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Buffer     = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer),\n    crypto     = __webpack_require__(/*! crypto */ \"crypto\"),\n    url        = __webpack_require__(/*! url */ \"url\"),\n    util       = __webpack_require__(/*! util */ \"util\"),\n    HttpParser = __webpack_require__(/*! ../http_parser */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/http_parser.js\"),\n    Base       = __webpack_require__(/*! ./base */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/base.js\"),\n    Hybi       = __webpack_require__(/*! ./hybi */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi.js\"),\n    Proxy      = __webpack_require__(/*! ./proxy */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/proxy.js\");\n\nvar Client = function(_url, options) {\n  this.version = 'hybi-' + Hybi.VERSION;\n  Hybi.call(this, null, _url, options);\n\n  this.readyState = -1;\n  this._key       = Client.generateKey();\n  this._accept    = Hybi.generateAccept(this._key);\n  this._http      = new HttpParser('response');\n\n  var uri  = url.parse(this.url),\n      auth = uri.auth && Buffer.from(uri.auth, 'utf8').toString('base64');\n\n  if (this.VALID_PROTOCOLS.indexOf(uri.protocol) < 0)\n    throw new Error(this.url + ' is not a valid WebSocket URL');\n\n  this._pathname = (uri.pathname || '/') + (uri.search || '');\n\n  this._headers.set('Host', uri.host);\n  this._headers.set('Upgrade', 'websocket');\n  this._headers.set('Connection', 'Upgrade');\n  this._headers.set('Sec-WebSocket-Key', this._key);\n  this._headers.set('Sec-WebSocket-Version', Hybi.VERSION);\n\n  if (this._protocols.length > 0)\n    this._headers.set('Sec-WebSocket-Protocol', this._protocols.join(', '));\n\n  if (auth)\n    this._headers.set('Authorization', 'Basic ' + auth);\n};\nutil.inherits(Client, Hybi);\n\nClient.generateKey = function() {\n  return crypto.randomBytes(16).toString('base64');\n};\n\nvar instance = {\n  VALID_PROTOCOLS: ['ws:', 'wss:'],\n\n  proxy: function(origin, options) {\n    return new Proxy(this, origin, options);\n  },\n\n  start: function() {\n    if (this.readyState !== -1) return false;\n    this._write(this._handshakeRequest());\n    this.readyState = 0;\n    return true;\n  },\n\n  parse: function(chunk) {\n    if (this.readyState === 3) return;\n    if (this.readyState > 0) return Hybi.prototype.parse.call(this, chunk);\n\n    this._http.parse(chunk);\n    if (!this._http.isComplete()) return;\n\n    this._validateHandshake();\n    if (this.readyState === 3) return;\n\n    this._open();\n    this.parse(this._http.body);\n  },\n\n  _handshakeRequest: function() {\n    var extensions = this._extensions.generateOffer();\n    if (extensions)\n      this._headers.set('Sec-WebSocket-Extensions', extensions);\n\n    var start   = 'GET ' + this._pathname + ' HTTP/1.1',\n        headers = [start, this._headers.toString(), ''];\n\n    return Buffer.from(headers.join('\\r\\n'), 'utf8');\n  },\n\n  _failHandshake: function(message) {\n    message = 'Error during WebSocket handshake: ' + message;\n    this.readyState = 3;\n    this.emit('error', new Error(message));\n    this.emit('close', new Base.CloseEvent(this.ERRORS.protocol_error, message));\n  },\n\n  _validateHandshake: function() {\n    this.statusCode = this._http.statusCode;\n    this.headers    = this._http.headers;\n\n    if (this._http.error)\n      return this._failHandshake(this._http.error.message);\n\n    if (this._http.statusCode !== 101)\n      return this._failHandshake('Unexpected response code: ' + this._http.statusCode);\n\n    var headers    = this._http.headers,\n        upgrade    = headers['upgrade'] || '',\n        connection = headers['connection'] || '',\n        accept     = headers['sec-websocket-accept'] || '',\n        protocol   = headers['sec-websocket-protocol'] || '';\n\n    if (upgrade === '')\n      return this._failHandshake(\"'Upgrade' header is missing\");\n    if (upgrade.toLowerCase() !== 'websocket')\n      return this._failHandshake(\"'Upgrade' header value is not 'WebSocket'\");\n\n    if (connection === '')\n      return this._failHandshake(\"'Connection' header is missing\");\n    if (connection.toLowerCase() !== 'upgrade')\n      return this._failHandshake(\"'Connection' header value is not 'Upgrade'\");\n\n    if (accept !== this._accept)\n      return this._failHandshake('Sec-WebSocket-Accept mismatch');\n\n    this.protocol = null;\n\n    if (protocol !== '') {\n      if (this._protocols.indexOf(protocol) < 0)\n        return this._failHandshake('Sec-WebSocket-Protocol mismatch');\n      else\n        this.protocol = protocol;\n    }\n\n    try {\n      this._extensions.activate(this.headers['sec-websocket-extensions']);\n    } catch (e) {\n      return this._failHandshake(e.message);\n    }\n  }\n};\n\nfor (var key in instance)\n  Client.prototype[key] = instance[key];\n\nmodule.exports = Client;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/draft75.js":
/*!***********************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/draft75.js ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Buffer = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer),\n    Base   = __webpack_require__(/*! ./base */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/base.js\"),\n    util   = __webpack_require__(/*! util */ \"util\");\n\nvar Draft75 = function(request, url, options) {\n  Base.apply(this, arguments);\n  this._stage  = 0;\n  this.version = 'hixie-75';\n\n  this._headers.set('Upgrade', 'WebSocket');\n  this._headers.set('Connection', 'Upgrade');\n  this._headers.set('WebSocket-Origin', this._request.headers.origin);\n  this._headers.set('WebSocket-Location', this.url);\n};\nutil.inherits(Draft75, Base);\n\nvar instance = {\n  close: function() {\n    if (this.readyState === 3) return false;\n    this.readyState = 3;\n    this.emit('close', new Base.CloseEvent(null, null));\n    return true;\n  },\n\n  parse: function(chunk) {\n    if (this.readyState > 1) return;\n\n    this._reader.put(chunk);\n\n    this._reader.eachByte(function(octet) {\n      var message;\n\n      switch (this._stage) {\n        case -1:\n          this._body.push(octet);\n          this._sendHandshakeBody();\n          break;\n\n        case 0:\n          this._parseLeadingByte(octet);\n          break;\n\n        case 1:\n          this._length = (octet & 0x7F) + 128 * this._length;\n\n          if (this._closing && this._length === 0) {\n            return this.close();\n          }\n          else if ((octet & 0x80) !== 0x80) {\n            if (this._length === 0) {\n              this._stage = 0;\n            }\n            else {\n              this._skipped = 0;\n              this._stage   = 2;\n            }\n          }\n          break;\n\n        case 2:\n          if (octet === 0xFF) {\n            this._stage = 0;\n            message = Buffer.from(this._buffer).toString('utf8', 0, this._buffer.length);\n            this.emit('message', new Base.MessageEvent(message));\n          }\n          else {\n            if (this._length) {\n              this._skipped += 1;\n              if (this._skipped === this._length)\n                this._stage = 0;\n            } else {\n              this._buffer.push(octet);\n              if (this._buffer.length > this._maxLength) return this.close();\n            }\n          }\n          break;\n      }\n    }, this);\n  },\n\n  frame: function(buffer) {\n    if (this.readyState === 0) return this._queue([buffer]);\n    if (this.readyState > 1) return false;\n\n    if (typeof buffer !== 'string') buffer = buffer.toString();\n\n    var length = Buffer.byteLength(buffer),\n        frame  = Buffer.allocUnsafe(length + 2);\n\n    frame[0] = 0x00;\n    frame.write(buffer, 1);\n    frame[frame.length - 1] = 0xFF;\n\n    this._write(frame);\n    return true;\n  },\n\n  _handshakeResponse: function() {\n    var start   = 'HTTP/1.1 101 Web Socket Protocol Handshake',\n        headers = [start, this._headers.toString(), ''];\n\n    return Buffer.from(headers.join('\\r\\n'), 'utf8');\n  },\n\n  _parseLeadingByte: function(octet) {\n    if ((octet & 0x80) === 0x80) {\n      this._length = 0;\n      this._stage  = 1;\n    } else {\n      delete this._length;\n      delete this._skipped;\n      this._buffer = [];\n      this._stage  = 2;\n    }\n  }\n};\n\nfor (var key in instance)\n  Draft75.prototype[key] = instance[key];\n\nmodule.exports = Draft75;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/draft75.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/draft76.js":
/*!***********************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/draft76.js ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Buffer  = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer),\n    Base    = __webpack_require__(/*! ./base */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/base.js\"),\n    Draft75 = __webpack_require__(/*! ./draft75 */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/draft75.js\"),\n    crypto  = __webpack_require__(/*! crypto */ \"crypto\"),\n    util    = __webpack_require__(/*! util */ \"util\");\n\n\nvar numberFromKey = function(key) {\n  return parseInt((key.match(/[0-9]/g) || []).join(''), 10);\n};\n\nvar spacesInKey = function(key) {\n  return (key.match(/ /g) || []).length;\n};\n\n\nvar Draft76 = function(request, url, options) {\n  Draft75.apply(this, arguments);\n  this._stage  = -1;\n  this._body   = [];\n  this.version = 'hixie-76';\n\n  this._headers.clear();\n\n  this._headers.set('Upgrade', 'WebSocket');\n  this._headers.set('Connection', 'Upgrade');\n  this._headers.set('Sec-WebSocket-Origin', this._request.headers.origin);\n  this._headers.set('Sec-WebSocket-Location', this.url);\n};\nutil.inherits(Draft76, Draft75);\n\nvar instance = {\n  BODY_SIZE: 8,\n\n  start: function() {\n    if (!Draft75.prototype.start.call(this)) return false;\n    this._started = true;\n    this._sendHandshakeBody();\n    return true;\n  },\n\n  close: function() {\n    if (this.readyState === 3) return false;\n    if (this.readyState === 1) this._write(Buffer.from([0xFF, 0x00]));\n    this.readyState = 3;\n    this.emit('close', new Base.CloseEvent(null, null));\n    return true;\n  },\n\n  _handshakeResponse: function() {\n    var headers = this._request.headers,\n        key1    = headers['sec-websocket-key1'],\n        key2    = headers['sec-websocket-key2'];\n\n    if (!key1) throw new Error('Missing required header: Sec-WebSocket-Key1');\n    if (!key2) throw new Error('Missing required header: Sec-WebSocket-Key2');\n\n    var number1 = numberFromKey(key1),\n        spaces1 = spacesInKey(key1),\n\n        number2 = numberFromKey(key2),\n        spaces2 = spacesInKey(key2);\n\n    if (number1 % spaces1 !== 0 || number2 % spaces2 !== 0)\n      throw new Error('Client sent invalid Sec-WebSocket-Key headers');\n\n    this._keyValues = [number1 / spaces1, number2 / spaces2];\n\n    var start   = 'HTTP/1.1 101 WebSocket Protocol Handshake',\n        headers = [start, this._headers.toString(), ''];\n\n    return Buffer.from(headers.join('\\r\\n'), 'binary');\n  },\n\n  _handshakeSignature: function() {\n    if (this._body.length < this.BODY_SIZE) return null;\n\n    var md5    = crypto.createHash('md5'),\n        buffer = Buffer.allocUnsafe(8 + this.BODY_SIZE);\n\n    buffer.writeUInt32BE(this._keyValues[0], 0);\n    buffer.writeUInt32BE(this._keyValues[1], 4);\n    Buffer.from(this._body).copy(buffer, 8, 0, this.BODY_SIZE);\n\n    md5.update(buffer);\n    return Buffer.from(md5.digest('binary'), 'binary');\n  },\n\n  _sendHandshakeBody: function() {\n    if (!this._started) return;\n    var signature = this._handshakeSignature();\n    if (!signature) return;\n\n    this._write(signature);\n    this._stage = 0;\n    this._open();\n\n    if (this._body.length > this.BODY_SIZE)\n      this.parse(this._body.slice(this.BODY_SIZE));\n  },\n\n  _parseLeadingByte: function(octet) {\n    if (octet !== 0xFF)\n      return Draft75.prototype._parseLeadingByte.call(this, octet);\n\n    this._closing = true;\n    this._length  = 0;\n    this._stage   = 1;\n  }\n};\n\nfor (var key in instance)\n  Draft76.prototype[key] = instance[key];\n\nmodule.exports = Draft76;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/draft76.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/headers.js":
/*!***********************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/headers.js ***!
  \***********************************************************************/
/***/ ((module) => {

eval("\n\nvar Headers = function() {\n  this.clear();\n};\n\nHeaders.prototype.ALLOWED_DUPLICATES = ['set-cookie', 'set-cookie2', 'warning', 'www-authenticate'];\n\nHeaders.prototype.clear = function() {\n  this._sent  = {};\n  this._lines = [];\n};\n\nHeaders.prototype.set = function(name, value) {\n  if (value === undefined) return;\n\n  name = this._strip(name);\n  value = this._strip(value);\n\n  var key = name.toLowerCase();\n  if (!this._sent.hasOwnProperty(key) || this.ALLOWED_DUPLICATES.indexOf(key) >= 0) {\n    this._sent[key] = true;\n    this._lines.push(name + ': ' + value + '\\r\\n');\n  }\n};\n\nHeaders.prototype.toString = function() {\n  return this._lines.join('');\n};\n\nHeaders.prototype._strip = function(string) {\n  return string.toString().replace(/^ */, '').replace(/ *$/, '');\n};\n\nmodule.exports = Headers;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2Vic29ja2V0LWRyaXZlci9saWIvd2Vic29ja2V0L2RyaXZlci9oZWFkZXJzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZWhpeC8uL25vZGVfbW9kdWxlcy93ZWJzb2NrZXQtZHJpdmVyL2xpYi93ZWJzb2NrZXQvZHJpdmVyL2hlYWRlcnMuanM/NDdiMiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciBIZWFkZXJzID0gZnVuY3Rpb24oKSB7XG4gIHRoaXMuY2xlYXIoKTtcbn07XG5cbkhlYWRlcnMucHJvdG90eXBlLkFMTE9XRURfRFVQTElDQVRFUyA9IFsnc2V0LWNvb2tpZScsICdzZXQtY29va2llMicsICd3YXJuaW5nJywgJ3d3dy1hdXRoZW50aWNhdGUnXTtcblxuSGVhZGVycy5wcm90b3R5cGUuY2xlYXIgPSBmdW5jdGlvbigpIHtcbiAgdGhpcy5fc2VudCAgPSB7fTtcbiAgdGhpcy5fbGluZXMgPSBbXTtcbn07XG5cbkhlYWRlcnMucHJvdG90eXBlLnNldCA9IGZ1bmN0aW9uKG5hbWUsIHZhbHVlKSB7XG4gIGlmICh2YWx1ZSA9PT0gdW5kZWZpbmVkKSByZXR1cm47XG5cbiAgbmFtZSA9IHRoaXMuX3N0cmlwKG5hbWUpO1xuICB2YWx1ZSA9IHRoaXMuX3N0cmlwKHZhbHVlKTtcblxuICB2YXIga2V5ID0gbmFtZS50b0xvd2VyQ2FzZSgpO1xuICBpZiAoIXRoaXMuX3NlbnQuaGFzT3duUHJvcGVydHkoa2V5KSB8fCB0aGlzLkFMTE9XRURfRFVQTElDQVRFUy5pbmRleE9mKGtleSkgPj0gMCkge1xuICAgIHRoaXMuX3NlbnRba2V5XSA9IHRydWU7XG4gICAgdGhpcy5fbGluZXMucHVzaChuYW1lICsgJzogJyArIHZhbHVlICsgJ1xcclxcbicpO1xuICB9XG59O1xuXG5IZWFkZXJzLnByb3RvdHlwZS50b1N0cmluZyA9IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gdGhpcy5fbGluZXMuam9pbignJyk7XG59O1xuXG5IZWFkZXJzLnByb3RvdHlwZS5fc3RyaXAgPSBmdW5jdGlvbihzdHJpbmcpIHtcbiAgcmV0dXJuIHN0cmluZy50b1N0cmluZygpLnJlcGxhY2UoL14gKi8sICcnKS5yZXBsYWNlKC8gKiQvLCAnJyk7XG59O1xuXG5tb2R1bGUuZXhwb3J0cyA9IEhlYWRlcnM7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/headers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi.js":
/*!********************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/hybi.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Buffer     = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer),\n    crypto     = __webpack_require__(/*! crypto */ \"crypto\"),\n    util       = __webpack_require__(/*! util */ \"util\"),\n    Extensions = __webpack_require__(/*! websocket-extensions */ \"(ssr)/./node_modules/websocket-extensions/lib/websocket_extensions.js\"),\n    Base       = __webpack_require__(/*! ./base */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/base.js\"),\n    Frame      = __webpack_require__(/*! ./hybi/frame */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi/frame.js\"),\n    Message    = __webpack_require__(/*! ./hybi/message */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi/message.js\");\n\nvar Hybi = function(request, url, options) {\n  Base.apply(this, arguments);\n\n  this._extensions     = new Extensions();\n  this._stage          = 0;\n  this._masking        = this._options.masking;\n  this._protocols      = this._options.protocols || [];\n  this._requireMasking = this._options.requireMasking;\n  this._pingCallbacks  = {};\n\n  if (typeof this._protocols === 'string')\n    this._protocols = this._protocols.split(/ *, */);\n\n  if (!this._request) return;\n\n  var protos    = this._request.headers['sec-websocket-protocol'],\n      supported = this._protocols;\n\n  if (protos !== undefined) {\n    if (typeof protos === 'string') protos = protos.split(/ *, */);\n    this.protocol = protos.filter(function(p) { return supported.indexOf(p) >= 0 })[0];\n  }\n\n  this.version = 'hybi-' + Hybi.VERSION;\n};\nutil.inherits(Hybi, Base);\n\nHybi.VERSION = '13';\n\nHybi.mask = function(payload, mask, offset) {\n  if (!mask || mask.length === 0) return payload;\n  offset = offset || 0;\n\n  for (var i = 0, n = payload.length - offset; i < n; i++) {\n    payload[offset + i] = payload[offset + i] ^ mask[i % 4];\n  }\n  return payload;\n};\n\nHybi.generateAccept = function(key) {\n  var sha1 = crypto.createHash('sha1');\n  sha1.update(key + Hybi.GUID);\n  return sha1.digest('base64');\n};\n\nHybi.GUID = '258EAFA5-E914-47DA-95CA-C5AB0DC85B11';\n\nvar instance = {\n  FIN:    0x80,\n  MASK:   0x80,\n  RSV1:   0x40,\n  RSV2:   0x20,\n  RSV3:   0x10,\n  OPCODE: 0x0F,\n  LENGTH: 0x7F,\n\n  OPCODES: {\n    continuation: 0,\n    text:         1,\n    binary:       2,\n    close:        8,\n    ping:         9,\n    pong:         10\n  },\n\n  OPCODE_CODES:    [0, 1, 2, 8, 9, 10],\n  MESSAGE_OPCODES: [0, 1, 2],\n  OPENING_OPCODES: [1, 2],\n\n  ERRORS: {\n    normal_closure:       1000,\n    going_away:           1001,\n    protocol_error:       1002,\n    unacceptable:         1003,\n    encoding_error:       1007,\n    policy_violation:     1008,\n    too_large:            1009,\n    extension_error:      1010,\n    unexpected_condition: 1011\n  },\n\n  ERROR_CODES:        [1000, 1001, 1002, 1003, 1007, 1008, 1009, 1010, 1011],\n  DEFAULT_ERROR_CODE: 1000,\n  MIN_RESERVED_ERROR: 3000,\n  MAX_RESERVED_ERROR: 4999,\n\n  // http://www.w3.org/International/questions/qa-forms-utf-8.en.php\n  UTF8_MATCH: /^([\\x00-\\x7F]|[\\xC2-\\xDF][\\x80-\\xBF]|\\xE0[\\xA0-\\xBF][\\x80-\\xBF]|[\\xE1-\\xEC\\xEE\\xEF][\\x80-\\xBF]{2}|\\xED[\\x80-\\x9F][\\x80-\\xBF]|\\xF0[\\x90-\\xBF][\\x80-\\xBF]{2}|[\\xF1-\\xF3][\\x80-\\xBF]{3}|\\xF4[\\x80-\\x8F][\\x80-\\xBF]{2})*$/,\n\n  addExtension: function(extension) {\n    this._extensions.add(extension);\n    return true;\n  },\n\n  parse: function(chunk) {\n    this._reader.put(chunk);\n    var buffer = true;\n    while (buffer) {\n      switch (this._stage) {\n        case 0:\n          buffer = this._reader.read(1);\n          if (buffer) this._parseOpcode(buffer[0]);\n          break;\n\n        case 1:\n          buffer = this._reader.read(1);\n          if (buffer) this._parseLength(buffer[0]);\n          break;\n\n        case 2:\n          buffer = this._reader.read(this._frame.lengthBytes);\n          if (buffer) this._parseExtendedLength(buffer);\n          break;\n\n        case 3:\n          buffer = this._reader.read(4);\n          if (buffer) {\n            this._stage = 4;\n            this._frame.maskingKey = buffer;\n          }\n          break;\n\n        case 4:\n          buffer = this._reader.read(this._frame.length);\n          if (buffer) {\n            this._stage = 0;\n            this._emitFrame(buffer);\n          }\n          break;\n\n        default:\n          buffer = null;\n      }\n    }\n  },\n\n  text: function(message) {\n    if (this.readyState > 1) return false;\n    return this.frame(message, 'text');\n  },\n\n  binary: function(message) {\n    if (this.readyState > 1) return false;\n    return this.frame(message, 'binary');\n  },\n\n  ping: function(message, callback) {\n    if (this.readyState > 1) return false;\n    message = message || '';\n    if (callback) this._pingCallbacks[message] = callback;\n    return this.frame(message, 'ping');\n  },\n\n  pong: function(message) {\n      if (this.readyState > 1) return false;\n      message = message ||'';\n      return this.frame(message, 'pong');\n  },\n\n  close: function(reason, code) {\n    reason = reason || '';\n    code   = code   || this.ERRORS.normal_closure;\n\n    if (this.readyState <= 0) {\n      this.readyState = 3;\n      this.emit('close', new Base.CloseEvent(code, reason));\n      return true;\n    } else if (this.readyState === 1) {\n      this.readyState = 2;\n      this._extensions.close(function() { this.frame(reason, 'close', code) }, this);\n      return true;\n    } else {\n      return false;\n    }\n  },\n\n  frame: function(buffer, type, code) {\n    if (this.readyState <= 0) return this._queue([buffer, type, code]);\n    if (this.readyState > 2) return false;\n\n    if (buffer instanceof Array)    buffer = Buffer.from(buffer);\n    if (typeof buffer === 'number') buffer = buffer.toString();\n\n    var message = new Message(),\n        isText  = (typeof buffer === 'string'),\n        payload, copy;\n\n    message.rsv1   = message.rsv2 = message.rsv3 = false;\n    message.opcode = this.OPCODES[type || (isText ? 'text' : 'binary')];\n\n    payload = isText ? Buffer.from(buffer, 'utf8') : buffer;\n\n    if (code) {\n      copy = payload;\n      payload = Buffer.allocUnsafe(2 + copy.length);\n      payload.writeUInt16BE(code, 0);\n      copy.copy(payload, 2);\n    }\n    message.data = payload;\n\n    var onMessageReady = function(message) {\n      var frame = new Frame();\n\n      frame.final   = true;\n      frame.rsv1    = message.rsv1;\n      frame.rsv2    = message.rsv2;\n      frame.rsv3    = message.rsv3;\n      frame.opcode  = message.opcode;\n      frame.masked  = !!this._masking;\n      frame.length  = message.data.length;\n      frame.payload = message.data;\n\n      if (frame.masked) frame.maskingKey = crypto.randomBytes(4);\n\n      this._sendFrame(frame);\n    };\n\n    if (this.MESSAGE_OPCODES.indexOf(message.opcode) >= 0)\n      this._extensions.processOutgoingMessage(message, function(error, message) {\n        if (error) return this._fail('extension_error', error.message);\n        onMessageReady.call(this, message);\n      }, this);\n    else\n      onMessageReady.call(this, message);\n\n    return true;\n  },\n\n  _sendFrame: function(frame) {\n    var length = frame.length,\n        header = (length <= 125) ? 2 : (length <= 65535 ? 4 : 10),\n        offset = header + (frame.masked ? 4 : 0),\n        buffer = Buffer.allocUnsafe(offset + length),\n        masked = frame.masked ? this.MASK : 0;\n\n    buffer[0] = (frame.final ? this.FIN : 0) |\n                (frame.rsv1 ? this.RSV1 : 0) |\n                (frame.rsv2 ? this.RSV2 : 0) |\n                (frame.rsv3 ? this.RSV3 : 0) |\n                frame.opcode;\n\n    if (length <= 125) {\n      buffer[1] = masked | length;\n    } else if (length <= 65535) {\n      buffer[1] = masked | 126;\n      buffer.writeUInt16BE(length, 2);\n    } else {\n      buffer[1] = masked | 127;\n      buffer.writeUInt32BE(Math.floor(length / 0x100000000), 2);\n      buffer.writeUInt32BE(length % 0x100000000, 6);\n    }\n\n    frame.payload.copy(buffer, offset);\n\n    if (frame.masked) {\n      frame.maskingKey.copy(buffer, header);\n      Hybi.mask(buffer, frame.maskingKey, offset);\n    }\n\n    this._write(buffer);\n  },\n\n  _handshakeResponse: function() {\n    var secKey  = this._request.headers['sec-websocket-key'],\n        version = this._request.headers['sec-websocket-version'];\n\n    if (version !== Hybi.VERSION)\n      throw new Error('Unsupported WebSocket version: ' + version);\n\n    if (typeof secKey !== 'string')\n      throw new Error('Missing handshake request header: Sec-WebSocket-Key');\n\n    this._headers.set('Upgrade', 'websocket');\n    this._headers.set('Connection', 'Upgrade');\n    this._headers.set('Sec-WebSocket-Accept', Hybi.generateAccept(secKey));\n\n    if (this.protocol) this._headers.set('Sec-WebSocket-Protocol', this.protocol);\n\n    var extensions = this._extensions.generateResponse(this._request.headers['sec-websocket-extensions']);\n    if (extensions) this._headers.set('Sec-WebSocket-Extensions', extensions);\n\n    var start   = 'HTTP/1.1 101 Switching Protocols',\n        headers = [start, this._headers.toString(), ''];\n\n    return Buffer.from(headers.join('\\r\\n'), 'utf8');\n  },\n\n  _shutdown: function(code, reason, error) {\n    delete this._frame;\n    delete this._message;\n    this._stage = 5;\n\n    var sendCloseFrame = (this.readyState === 1);\n    this.readyState = 2;\n\n    this._extensions.close(function() {\n      if (sendCloseFrame) this.frame(reason, 'close', code);\n      this.readyState = 3;\n      if (error) this.emit('error', new Error(reason));\n      this.emit('close', new Base.CloseEvent(code, reason));\n    }, this);\n  },\n\n  _fail: function(type, message) {\n    if (this.readyState > 1) return;\n    this._shutdown(this.ERRORS[type], message, true);\n  },\n\n  _parseOpcode: function(octet) {\n    var rsvs = [this.RSV1, this.RSV2, this.RSV3].map(function(rsv) {\n      return (octet & rsv) === rsv;\n    });\n\n    var frame = this._frame = new Frame();\n\n    frame.final  = (octet & this.FIN) === this.FIN;\n    frame.rsv1   = rsvs[0];\n    frame.rsv2   = rsvs[1];\n    frame.rsv3   = rsvs[2];\n    frame.opcode = (octet & this.OPCODE);\n\n    this._stage = 1;\n\n    if (!this._extensions.validFrameRsv(frame))\n      return this._fail('protocol_error',\n          'One or more reserved bits are on: reserved1 = ' + (frame.rsv1 ? 1 : 0) +\n          ', reserved2 = ' + (frame.rsv2 ? 1 : 0) +\n          ', reserved3 = ' + (frame.rsv3 ? 1 : 0));\n\n    if (this.OPCODE_CODES.indexOf(frame.opcode) < 0)\n      return this._fail('protocol_error', 'Unrecognized frame opcode: ' + frame.opcode);\n\n    if (this.MESSAGE_OPCODES.indexOf(frame.opcode) < 0 && !frame.final)\n      return this._fail('protocol_error', 'Received fragmented control frame: opcode = ' + frame.opcode);\n\n    if (this._message && this.OPENING_OPCODES.indexOf(frame.opcode) >= 0)\n      return this._fail('protocol_error', 'Received new data frame but previous continuous frame is unfinished');\n  },\n\n  _parseLength: function(octet) {\n    var frame = this._frame;\n    frame.masked = (octet & this.MASK) === this.MASK;\n    frame.length = (octet & this.LENGTH);\n\n    if (frame.length >= 0 && frame.length <= 125) {\n      this._stage = frame.masked ? 3 : 4;\n      if (!this._checkFrameLength()) return;\n    } else {\n      this._stage = 2;\n      frame.lengthBytes = (frame.length === 126 ? 2 : 8);\n    }\n\n    if (this._requireMasking && !frame.masked)\n      return this._fail('unacceptable', 'Received unmasked frame but masking is required');\n  },\n\n  _parseExtendedLength: function(buffer) {\n    var frame = this._frame;\n    frame.length = this._readUInt(buffer);\n\n    this._stage = frame.masked ? 3 : 4;\n\n    if (this.MESSAGE_OPCODES.indexOf(frame.opcode) < 0 && frame.length > 125)\n      return this._fail('protocol_error', 'Received control frame having too long payload: ' + frame.length);\n\n    if (!this._checkFrameLength()) return;\n  },\n\n  _checkFrameLength: function() {\n    var length = this._message ? this._message.length : 0;\n\n    if (length + this._frame.length > this._maxLength) {\n      this._fail('too_large', 'WebSocket frame length too large');\n      return false;\n    } else {\n      return true;\n    }\n  },\n\n  _emitFrame: function(buffer) {\n    var frame   = this._frame,\n        payload = frame.payload = Hybi.mask(buffer, frame.maskingKey),\n        opcode  = frame.opcode,\n        message,\n        code, reason,\n        callbacks, callback;\n\n    delete this._frame;\n\n    if (opcode === this.OPCODES.continuation) {\n      if (!this._message) return this._fail('protocol_error', 'Received unexpected continuation frame');\n      this._message.pushFrame(frame);\n    }\n\n    if (opcode === this.OPCODES.text || opcode === this.OPCODES.binary) {\n      this._message = new Message();\n      this._message.pushFrame(frame);\n    }\n\n    if (frame.final && this.MESSAGE_OPCODES.indexOf(opcode) >= 0)\n      return this._emitMessage(this._message);\n\n    if (opcode === this.OPCODES.close) {\n      code   = (payload.length >= 2) ? payload.readUInt16BE(0) : null;\n      reason = (payload.length > 2) ? this._encode(payload.slice(2)) : null;\n\n      if (!(payload.length === 0) &&\n          !(code !== null && code >= this.MIN_RESERVED_ERROR && code <= this.MAX_RESERVED_ERROR) &&\n          this.ERROR_CODES.indexOf(code) < 0)\n        code = this.ERRORS.protocol_error;\n\n      if (payload.length > 125 || (payload.length > 2 && !reason))\n        code = this.ERRORS.protocol_error;\n\n      this._shutdown(code || this.DEFAULT_ERROR_CODE, reason || '');\n    }\n\n    if (opcode === this.OPCODES.ping) {\n      this.frame(payload, 'pong');\n      this.emit('ping', new Base.PingEvent(payload.toString()))\n    }\n\n    if (opcode === this.OPCODES.pong) {\n      callbacks = this._pingCallbacks;\n      message   = this._encode(payload);\n      callback  = callbacks[message];\n\n      delete callbacks[message];\n      if (callback) callback()\n\n      this.emit('pong', new Base.PongEvent(payload.toString()))\n    }\n  },\n\n  _emitMessage: function(message) {\n    var message = this._message;\n    message.read();\n\n    delete this._message;\n\n    this._extensions.processIncomingMessage(message, function(error, message) {\n      if (error) return this._fail('extension_error', error.message);\n\n      var payload = message.data;\n      if (message.opcode === this.OPCODES.text) payload = this._encode(payload);\n\n      if (payload === null)\n        return this._fail('encoding_error', 'Could not decode a text frame as UTF-8');\n      else\n        this.emit('message', new Base.MessageEvent(payload));\n    }, this);\n  },\n\n  _encode: function(buffer) {\n    try {\n      var string = buffer.toString('binary', 0, buffer.length);\n      if (!this.UTF8_MATCH.test(string)) return null;\n    } catch (e) {}\n    return buffer.toString('utf8', 0, buffer.length);\n  },\n\n  _readUInt: function(buffer) {\n    if (buffer.length === 2) return buffer.readUInt16BE(0);\n\n    return buffer.readUInt32BE(0) * 0x100000000 +\n           buffer.readUInt32BE(4);\n  }\n};\n\nfor (var key in instance)\n  Hybi.prototype[key] = instance[key];\n\nmodule.exports = Hybi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi/frame.js":
/*!**************************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/hybi/frame.js ***!
  \**************************************************************************/
/***/ ((module) => {

eval("\n\nvar Frame = function() {};\n\nvar instance = {\n  final:        false,\n  rsv1:         false,\n  rsv2:         false,\n  rsv3:         false,\n  opcode:       null,\n  masked:       false,\n  maskingKey:   null,\n  lengthBytes:  1,\n  length:       0,\n  payload:      null\n};\n\nfor (var key in instance)\n  Frame.prototype[key] = instance[key];\n\nmodule.exports = Frame;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2Vic29ja2V0LWRyaXZlci9saWIvd2Vic29ja2V0L2RyaXZlci9oeWJpL2ZyYW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVoaXgvLi9ub2RlX21vZHVsZXMvd2Vic29ja2V0LWRyaXZlci9saWIvd2Vic29ja2V0L2RyaXZlci9oeWJpL2ZyYW1lLmpzPzY3YzkiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgRnJhbWUgPSBmdW5jdGlvbigpIHt9O1xuXG52YXIgaW5zdGFuY2UgPSB7XG4gIGZpbmFsOiAgICAgICAgZmFsc2UsXG4gIHJzdjE6ICAgICAgICAgZmFsc2UsXG4gIHJzdjI6ICAgICAgICAgZmFsc2UsXG4gIHJzdjM6ICAgICAgICAgZmFsc2UsXG4gIG9wY29kZTogICAgICAgbnVsbCxcbiAgbWFza2VkOiAgICAgICBmYWxzZSxcbiAgbWFza2luZ0tleTogICBudWxsLFxuICBsZW5ndGhCeXRlczogIDEsXG4gIGxlbmd0aDogICAgICAgMCxcbiAgcGF5bG9hZDogICAgICBudWxsXG59O1xuXG5mb3IgKHZhciBrZXkgaW4gaW5zdGFuY2UpXG4gIEZyYW1lLnByb3RvdHlwZVtrZXldID0gaW5zdGFuY2Vba2V5XTtcblxubW9kdWxlLmV4cG9ydHMgPSBGcmFtZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi/frame.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi/message.js":
/*!****************************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/hybi/message.js ***!
  \****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Buffer = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer);\n\nvar Message = function() {\n  this.rsv1    = false;\n  this.rsv2    = false;\n  this.rsv3    = false;\n  this.opcode  = null;\n  this.length  = 0;\n  this._chunks = [];\n};\n\nvar instance = {\n  read: function() {\n    return this.data = this.data || Buffer.concat(this._chunks, this.length);\n  },\n\n  pushFrame: function(frame) {\n    this.rsv1 = this.rsv1 || frame.rsv1;\n    this.rsv2 = this.rsv2 || frame.rsv2;\n    this.rsv3 = this.rsv3 || frame.rsv3;\n\n    if (this.opcode === null) this.opcode = frame.opcode;\n\n    this._chunks.push(frame.payload);\n    this.length += frame.length;\n  }\n};\n\nfor (var key in instance)\n  Message.prototype[key] = instance[key];\n\nmodule.exports = Message;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2Vic29ja2V0LWRyaXZlci9saWIvd2Vic29ja2V0L2RyaXZlci9oeWJpL21lc3NhZ2UuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsYUFBYSw0RkFBNkI7O0FBRTFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVoaXgvLi9ub2RlX21vZHVsZXMvd2Vic29ja2V0LWRyaXZlci9saWIvd2Vic29ja2V0L2RyaXZlci9oeWJpL21lc3NhZ2UuanM/YTdjNSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciBCdWZmZXIgPSByZXF1aXJlKCdzYWZlLWJ1ZmZlcicpLkJ1ZmZlcjtcblxudmFyIE1lc3NhZ2UgPSBmdW5jdGlvbigpIHtcbiAgdGhpcy5yc3YxICAgID0gZmFsc2U7XG4gIHRoaXMucnN2MiAgICA9IGZhbHNlO1xuICB0aGlzLnJzdjMgICAgPSBmYWxzZTtcbiAgdGhpcy5vcGNvZGUgID0gbnVsbDtcbiAgdGhpcy5sZW5ndGggID0gMDtcbiAgdGhpcy5fY2h1bmtzID0gW107XG59O1xuXG52YXIgaW5zdGFuY2UgPSB7XG4gIHJlYWQ6IGZ1bmN0aW9uKCkge1xuICAgIHJldHVybiB0aGlzLmRhdGEgPSB0aGlzLmRhdGEgfHwgQnVmZmVyLmNvbmNhdCh0aGlzLl9jaHVua3MsIHRoaXMubGVuZ3RoKTtcbiAgfSxcblxuICBwdXNoRnJhbWU6IGZ1bmN0aW9uKGZyYW1lKSB7XG4gICAgdGhpcy5yc3YxID0gdGhpcy5yc3YxIHx8IGZyYW1lLnJzdjE7XG4gICAgdGhpcy5yc3YyID0gdGhpcy5yc3YyIHx8IGZyYW1lLnJzdjI7XG4gICAgdGhpcy5yc3YzID0gdGhpcy5yc3YzIHx8IGZyYW1lLnJzdjM7XG5cbiAgICBpZiAodGhpcy5vcGNvZGUgPT09IG51bGwpIHRoaXMub3Bjb2RlID0gZnJhbWUub3Bjb2RlO1xuXG4gICAgdGhpcy5fY2h1bmtzLnB1c2goZnJhbWUucGF5bG9hZCk7XG4gICAgdGhpcy5sZW5ndGggKz0gZnJhbWUubGVuZ3RoO1xuICB9XG59O1xuXG5mb3IgKHZhciBrZXkgaW4gaW5zdGFuY2UpXG4gIE1lc3NhZ2UucHJvdG90eXBlW2tleV0gPSBpbnN0YW5jZVtrZXldO1xuXG5tb2R1bGUuZXhwb3J0cyA9IE1lc3NhZ2U7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi/message.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/proxy.js":
/*!*********************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/proxy.js ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Buffer     = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer),\n    Stream     = (__webpack_require__(/*! stream */ \"stream\").Stream),\n    url        = __webpack_require__(/*! url */ \"url\"),\n    util       = __webpack_require__(/*! util */ \"util\"),\n    Base       = __webpack_require__(/*! ./base */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/base.js\"),\n    Headers    = __webpack_require__(/*! ./headers */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/headers.js\"),\n    HttpParser = __webpack_require__(/*! ../http_parser */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/http_parser.js\");\n\nvar PORTS = { 'ws:': 80, 'wss:': 443 };\n\nvar Proxy = function(client, origin, options) {\n  this._client  = client;\n  this._http    = new HttpParser('response');\n  this._origin  = (typeof client.url === 'object') ? client.url : url.parse(client.url);\n  this._url     = (typeof origin === 'object') ? origin : url.parse(origin);\n  this._options = options || {};\n  this._state   = 0;\n\n  this.readable = this.writable = true;\n  this._paused  = false;\n\n  this._headers = new Headers();\n  this._headers.set('Host', this._origin.host);\n  this._headers.set('Connection', 'keep-alive');\n  this._headers.set('Proxy-Connection', 'keep-alive');\n\n  var auth = this._url.auth && Buffer.from(this._url.auth, 'utf8').toString('base64');\n  if (auth) this._headers.set('Proxy-Authorization', 'Basic ' + auth);\n};\nutil.inherits(Proxy, Stream);\n\nvar instance = {\n  setHeader: function(name, value) {\n    if (this._state !== 0) return false;\n    this._headers.set(name, value);\n    return true;\n  },\n\n  start: function() {\n    if (this._state !== 0) return false;\n    this._state = 1;\n\n    var origin = this._origin,\n        port   = origin.port || PORTS[origin.protocol],\n        start  = 'CONNECT ' + origin.hostname + ':' + port + ' HTTP/1.1';\n\n    var headers = [start, this._headers.toString(), ''];\n\n    this.emit('data', Buffer.from(headers.join('\\r\\n'), 'utf8'));\n    return true;\n  },\n\n  pause: function() {\n    this._paused = true;\n  },\n\n  resume: function() {\n    this._paused = false;\n    this.emit('drain');\n  },\n\n  write: function(chunk) {\n    if (!this.writable) return false;\n\n    this._http.parse(chunk);\n    if (!this._http.isComplete()) return !this._paused;\n\n    this.statusCode = this._http.statusCode;\n    this.headers    = this._http.headers;\n\n    if (this.statusCode === 200) {\n      this.emit('connect', new Base.ConnectEvent());\n    } else {\n      var message = \"Can't establish a connection to the server at \" + this._origin.href;\n      this.emit('error', new Error(message));\n    }\n    this.end();\n    return !this._paused;\n  },\n\n  end: function(chunk) {\n    if (!this.writable) return;\n    if (chunk !== undefined) this.write(chunk);\n    this.readable = this.writable = false;\n    this.emit('close');\n    this.emit('end');\n  },\n\n  destroy: function() {\n    this.end();\n  }\n};\n\nfor (var key in instance)\n  Proxy.prototype[key] = instance[key];\n\nmodule.exports = Proxy;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/proxy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/server.js":
/*!**********************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/server.js ***!
  \**********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar util       = __webpack_require__(/*! util */ \"util\"),\n    HttpParser = __webpack_require__(/*! ../http_parser */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/http_parser.js\"),\n    Base       = __webpack_require__(/*! ./base */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/base.js\"),\n    Draft75    = __webpack_require__(/*! ./draft75 */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/draft75.js\"),\n    Draft76    = __webpack_require__(/*! ./draft76 */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/draft76.js\"),\n    Hybi       = __webpack_require__(/*! ./hybi */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi.js\");\n\nvar Server = function(options) {\n  Base.call(this, null, null, options);\n  this._http = new HttpParser('request');\n};\nutil.inherits(Server, Base);\n\nvar instance = {\n  EVENTS: ['open', 'message', 'error', 'close', 'ping', 'pong'],\n\n  _bindEventListeners: function() {\n    this.messages.on('error', function() {});\n    this.on('error', function() {});\n  },\n\n  parse: function(chunk) {\n    if (this._delegate) return this._delegate.parse(chunk);\n\n    this._http.parse(chunk);\n    if (!this._http.isComplete()) return;\n\n    this.method  = this._http.method;\n    this.url     = this._http.url;\n    this.headers = this._http.headers;\n    this.body    = this._http.body;\n\n    var self = this;\n    this._delegate = Server.http(this, this._options);\n    this._delegate.messages = this.messages;\n    this._delegate.io = this.io;\n    this._open();\n\n    this.EVENTS.forEach(function(event) {\n      this._delegate.on(event, function(e) { self.emit(event, e) });\n    }, this);\n\n    this.protocol = this._delegate.protocol;\n    this.version  = this._delegate.version;\n\n    this.parse(this._http.body);\n    this.emit('connect', new Base.ConnectEvent());\n  },\n\n  _open: function() {\n    this.__queue.forEach(function(msg) {\n      this._delegate[msg[0]].apply(this._delegate, msg[1]);\n    }, this);\n    this.__queue = [];\n  }\n};\n\n['addExtension', 'setHeader', 'start', 'frame', 'text', 'binary', 'ping', 'close'].forEach(function(method) {\n  instance[method] = function() {\n    if (this._delegate) {\n      return this._delegate[method].apply(this._delegate, arguments);\n    } else {\n      this.__queue.push([method, arguments]);\n      return true;\n    }\n  };\n});\n\nfor (var key in instance)\n  Server.prototype[key] = instance[key];\n\nServer.isSecureRequest = function(request) {\n  if (request.connection && request.connection.authorized !== undefined) return true;\n  if (request.socket && request.socket.secure) return true;\n\n  var headers = request.headers;\n  if (!headers) return false;\n  if (headers['https'] === 'on') return true;\n  if (headers['x-forwarded-ssl'] === 'on') return true;\n  if (headers['x-forwarded-scheme'] === 'https') return true;\n  if (headers['x-forwarded-proto'] === 'https') return true;\n\n  return false;\n};\n\nServer.determineUrl = function(request) {\n  var scheme = this.isSecureRequest(request) ? 'wss:' : 'ws:';\n  return scheme + '//' + request.headers.host + request.url;\n};\n\nServer.http = function(request, options) {\n  options = options || {};\n  if (options.requireMasking === undefined) options.requireMasking = true;\n\n  var headers = request.headers,\n      version = headers['sec-websocket-version'],\n      key     = headers['sec-websocket-key'],\n      key1    = headers['sec-websocket-key1'],\n      key2    = headers['sec-websocket-key2'],\n      url     = this.determineUrl(request);\n\n  if (version || key)\n    return new Hybi(request, url, options);\n  else if (key1 || key2)\n    return new Draft76(request, url, options);\n  else\n    return new Draft75(request, url, options);\n};\n\nmodule.exports = Server;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/server.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/stream_reader.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/stream_reader.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Buffer = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer);\n\nvar StreamReader = function() {\n  this._queue     = [];\n  this._queueSize = 0;\n  this._offset    = 0;\n};\n\nStreamReader.prototype.put = function(buffer) {\n  if (!buffer || buffer.length === 0) return;\n  if (!Buffer.isBuffer(buffer)) buffer = Buffer.from(buffer);\n  this._queue.push(buffer);\n  this._queueSize += buffer.length;\n};\n\nStreamReader.prototype.read = function(length) {\n  if (length > this._queueSize) return null;\n  if (length === 0) return Buffer.alloc(0);\n\n  this._queueSize -= length;\n\n  var queue  = this._queue,\n      remain = length,\n      first  = queue[0],\n      buffers, buffer;\n\n  if (first.length >= length) {\n    if (first.length === length) {\n      return queue.shift();\n    } else {\n      buffer = first.slice(0, length);\n      queue[0] = first.slice(length);\n      return buffer;\n    }\n  }\n\n  for (var i = 0, n = queue.length; i < n; i++) {\n    if (remain < queue[i].length) break;\n    remain -= queue[i].length;\n  }\n  buffers = queue.splice(0, i);\n\n  if (remain > 0 && queue.length > 0) {\n    buffers.push(queue[0].slice(0, remain));\n    queue[0] = queue[0].slice(remain);\n  }\n  return Buffer.concat(buffers, length);\n};\n\nStreamReader.prototype.eachByte = function(callback, context) {\n  var buffer, n, index;\n\n  while (this._queue.length > 0) {\n    buffer = this._queue[0];\n    n = buffer.length;\n\n    while (this._offset < n) {\n      index = this._offset;\n      this._offset += 1;\n      callback.call(context, buffer[index]);\n    }\n    this._offset = 0;\n    this._queue.shift();\n  }\n};\n\nmodule.exports = StreamReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/stream_reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/http_parser.js":
/*!********************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/http_parser.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar NodeHTTPParser = (__webpack_require__(/*! http-parser-js */ \"(ssr)/./node_modules/http-parser-js/http-parser.js\").HTTPParser),\n    Buffer         = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer);\n\nvar TYPES = {\n  request:  NodeHTTPParser.REQUEST  || 'request',\n  response: NodeHTTPParser.RESPONSE || 'response'\n};\n\nvar HttpParser = function(type) {\n  this._type     = type;\n  this._parser   = new NodeHTTPParser(TYPES[type]);\n  this._complete = false;\n  this.headers   = {};\n\n  var current = null,\n      self    = this;\n\n  this._parser.onHeaderField = function(b, start, length) {\n    current = b.toString('utf8', start, start + length).toLowerCase();\n  };\n\n  this._parser.onHeaderValue = function(b, start, length) {\n    var value = b.toString('utf8', start, start + length);\n\n    if (self.headers.hasOwnProperty(current))\n      self.headers[current] += ', ' + value;\n    else\n      self.headers[current] = value;\n  };\n\n  this._parser.onHeadersComplete = this._parser[NodeHTTPParser.kOnHeadersComplete] =\n  function(majorVersion, minorVersion, headers, method, pathname, statusCode) {\n    var info = arguments[0];\n\n    if (typeof info === 'object') {\n      method     = info.method;\n      pathname   = info.url;\n      statusCode = info.statusCode;\n      headers    = info.headers;\n    }\n\n    self.method     = (typeof method === 'number') ? HttpParser.METHODS[method] : method;\n    self.statusCode = statusCode;\n    self.url        = pathname;\n\n    if (!headers) return;\n\n    for (var i = 0, n = headers.length, key, value; i < n; i += 2) {\n      key   = headers[i].toLowerCase();\n      value = headers[i+1];\n      if (self.headers.hasOwnProperty(key))\n        self.headers[key] += ', ' + value;\n      else\n        self.headers[key] = value;\n    }\n\n    self._complete = true;\n  };\n};\n\nHttpParser.METHODS = {\n  0:  'DELETE',\n  1:  'GET',\n  2:  'HEAD',\n  3:  'POST',\n  4:  'PUT',\n  5:  'CONNECT',\n  6:  'OPTIONS',\n  7:  'TRACE',\n  8:  'COPY',\n  9:  'LOCK',\n  10: 'MKCOL',\n  11: 'MOVE',\n  12: 'PROPFIND',\n  13: 'PROPPATCH',\n  14: 'SEARCH',\n  15: 'UNLOCK',\n  16: 'BIND',\n  17: 'REBIND',\n  18: 'UNBIND',\n  19: 'ACL',\n  20: 'REPORT',\n  21: 'MKACTIVITY',\n  22: 'CHECKOUT',\n  23: 'MERGE',\n  24: 'M-SEARCH',\n  25: 'NOTIFY',\n  26: 'SUBSCRIBE',\n  27: 'UNSUBSCRIBE',\n  28: 'PATCH',\n  29: 'PURGE',\n  30: 'MKCALENDAR',\n  31: 'LINK',\n  32: 'UNLINK'\n};\n\nvar VERSION = process.version\n  ? process.version.match(/[0-9]+/g).map(function(n) { return parseInt(n, 10) })\n  : [];\n\nif (VERSION[0] === 0 && VERSION[1] === 12) {\n  HttpParser.METHODS[16] = 'REPORT';\n  HttpParser.METHODS[17] = 'MKACTIVITY';\n  HttpParser.METHODS[18] = 'CHECKOUT';\n  HttpParser.METHODS[19] = 'MERGE';\n  HttpParser.METHODS[20] = 'M-SEARCH';\n  HttpParser.METHODS[21] = 'NOTIFY';\n  HttpParser.METHODS[22] = 'SUBSCRIBE';\n  HttpParser.METHODS[23] = 'UNSUBSCRIBE';\n  HttpParser.METHODS[24] = 'PATCH';\n  HttpParser.METHODS[25] = 'PURGE';\n}\n\nHttpParser.prototype.isComplete = function() {\n  return this._complete;\n};\n\nHttpParser.prototype.parse = function(chunk) {\n  var consumed = this._parser.execute(chunk, 0, chunk.length);\n\n  if (typeof consumed !== 'number') {\n    this.error     = consumed;\n    this._complete = true;\n    return;\n  }\n\n  if (this._complete)\n    this.body = (consumed < chunk.length)\n              ? chunk.slice(consumed)\n              : Buffer.alloc(0);\n};\n\nmodule.exports = HttpParser;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/http_parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/streams.js":
/*!****************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/streams.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\n/**\n\nStreams in a WebSocket connection\n---------------------------------\n\nWe model a WebSocket as two duplex streams: one stream is for the wire protocol\nover an I/O socket, and the other is for incoming/outgoing messages.\n\n\n                        +----------+      +---------+      +----------+\n    [1] write(chunk) -->| ~~~~~~~~ +----->| parse() +----->| ~~~~~~~~ +--> emit('data') [2]\n                        |          |      +----+----+      |          |\n                        |          |           |           |          |\n                        |    IO    |           | [5]       | Messages |\n                        |          |           V           |          |\n                        |          |      +---------+      |          |\n    [4] emit('data') <--+ ~~~~~~~~ |<-----+ frame() |<-----+ ~~~~~~~~ |<-- write(chunk) [3]\n                        +----------+      +---------+      +----------+\n\n\nMessage transfer in each direction is simple: IO receives a byte stream [1] and\nsends this stream for parsing. The parser will periodically emit a complete\nmessage text on the Messages stream [2]. Similarly, when messages are written\nto the Messages stream [3], they are framed using the WebSocket wire format and\nemitted via IO [4].\n\nThere is a feedback loop via [5] since some input from [1] will be things like\nping, pong and close frames. In these cases the protocol responds by emitting\nresponses directly back to [4] rather than emitting messages via [2].\n\nFor the purposes of flow control, we consider the sources of each Readable\nstream to be as follows:\n\n* [2] receives input from [1]\n* [4] receives input from [1] and [3]\n\nThe classes below express the relationships described above without prescribing\nanything about how parse() and frame() work, other than assuming they emit\n'data' events to the IO and Messages streams. They will work with any protocol\ndriver having these two methods.\n**/\n\n\nvar Stream = (__webpack_require__(/*! stream */ \"stream\").Stream),\n    util   = __webpack_require__(/*! util */ \"util\");\n\n\nvar IO = function(driver) {\n  this.readable = this.writable = true;\n  this._paused  = false;\n  this._driver  = driver;\n};\nutil.inherits(IO, Stream);\n\n// The IO pause() and resume() methods will be called when the socket we are\n// piping to gets backed up and drains. Since IO output [4] comes from IO input\n// [1] and Messages input [3], we need to tell both of those to return false\n// from write() when this stream is paused.\n\nIO.prototype.pause = function() {\n  this._paused = true;\n  this._driver.messages._paused = true;\n};\n\nIO.prototype.resume = function() {\n  this._paused = false;\n  this.emit('drain');\n\n  var messages = this._driver.messages;\n  messages._paused = false;\n  messages.emit('drain');\n};\n\n// When we receive input from a socket, send it to the parser and tell the\n// source whether to back off.\nIO.prototype.write = function(chunk) {\n  if (!this.writable) return false;\n  this._driver.parse(chunk);\n  return !this._paused;\n};\n\n// The IO end() method will be called when the socket piping into it emits\n// 'close' or 'end', i.e. the socket is closed. In this situation the Messages\n// stream will not emit any more data so we emit 'end'.\nIO.prototype.end = function(chunk) {\n  if (!this.writable) return;\n  if (chunk !== undefined) this.write(chunk);\n  this.writable = false;\n\n  var messages = this._driver.messages;\n  if (messages.readable) {\n    messages.readable = messages.writable = false;\n    messages.emit('end');\n  }\n};\n\nIO.prototype.destroy = function() {\n  this.end();\n};\n\n\nvar Messages = function(driver) {\n  this.readable = this.writable = true;\n  this._paused  = false;\n  this._driver  = driver;\n};\nutil.inherits(Messages, Stream);\n\n// The Messages pause() and resume() methods will be called when the app that's\n// processing the messages gets backed up and drains. If we're emitting\n// messages too fast we should tell the source to slow down. Message output [2]\n// comes from IO input [1].\n\nMessages.prototype.pause = function() {\n  this._driver.io._paused = true;\n};\n\nMessages.prototype.resume = function() {\n  this._driver.io._paused = false;\n  this._driver.io.emit('drain');\n};\n\n// When we receive messages from the user, send them to the formatter and tell\n// the source whether to back off.\nMessages.prototype.write = function(message) {\n  if (!this.writable) return false;\n  if (typeof message === 'string') this._driver.text(message);\n  else this._driver.binary(message);\n  return !this._paused;\n};\n\n// The Messages end() method will be called when a stream piping into it emits\n// 'end'. Many streams may be piped into the WebSocket and one of them ending\n// does not mean the whole socket is done, so just process the input and move\n// on leaving the socket open.\nMessages.prototype.end = function(message) {\n  if (message !== undefined) this.write(message);\n};\n\nMessages.prototype.destroy = function() {};\n\n\nexports.IO = IO;\nexports.Messages = Messages;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/streams.js\n");

/***/ })

};
;