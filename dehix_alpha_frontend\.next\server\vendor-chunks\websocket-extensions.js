"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/websocket-extensions";
exports.ids = ["vendor-chunks/websocket-extensions"];
exports.modules = {

/***/ "(ssr)/./node_modules/websocket-extensions/lib/parser.js":
/*!*********************************************************!*\
  !*** ./node_modules/websocket-extensions/lib/parser.js ***!
  \*********************************************************/
/***/ ((module) => {

eval("\n\nvar TOKEN    = /([!#\\$%&'\\*\\+\\-\\.\\^_`\\|~0-9A-Za-z]+)/,\n    NOTOKEN  = /([^!#\\$%&'\\*\\+\\-\\.\\^_`\\|~0-9A-Za-z])/g,\n    QUOTED   = /\"((?:\\\\[\\x00-\\x7f]|[^\\x00-\\x08\\x0a-\\x1f\\x7f\"\\\\])*)\"/,\n    PARAM    = new RegExp(TOKEN.source + '(?:=(?:' + TOKEN.source + '|' + QUOTED.source + '))?'),\n    EXT      = new RegExp(TOKEN.source + '(?: *; *' + PARAM.source + ')*', 'g'),\n    EXT_LIST = new RegExp('^' + EXT.source + '(?: *, *' + EXT.source + ')*$'),\n    NUMBER   = /^-?(0|[1-9][0-9]*)(\\.[0-9]+)?$/;\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar Parser = {\n  parseHeader: function(header) {\n    var offers = new Offers();\n    if (header === '' || header === undefined) return offers;\n\n    if (!EXT_LIST.test(header))\n      throw new SyntaxError('Invalid Sec-WebSocket-Extensions header: ' + header);\n\n    var values = header.match(EXT);\n\n    values.forEach(function(value) {\n      var params = value.match(new RegExp(PARAM.source, 'g')),\n          name   = params.shift(),\n          offer  = {};\n\n      params.forEach(function(param) {\n        var args = param.match(PARAM), key = args[1], data;\n\n        if (args[2] !== undefined) {\n          data = args[2];\n        } else if (args[3] !== undefined) {\n          data = args[3].replace(/\\\\/g, '');\n        } else {\n          data = true;\n        }\n        if (NUMBER.test(data)) data = parseFloat(data);\n\n        if (hasOwnProperty.call(offer, key)) {\n          offer[key] = [].concat(offer[key]);\n          offer[key].push(data);\n        } else {\n          offer[key] = data;\n        }\n      }, this);\n      offers.push(name, offer);\n    }, this);\n\n    return offers;\n  },\n\n  serializeParams: function(name, params) {\n    var values = [];\n\n    var print = function(key, value) {\n      if (value instanceof Array) {\n        value.forEach(function(v) { print(key, v) });\n      } else if (value === true) {\n        values.push(key);\n      } else if (typeof value === 'number') {\n        values.push(key + '=' + value);\n      } else if (NOTOKEN.test(value)) {\n        values.push(key + '=\"' + value.replace(/\"/g, '\\\\\"') + '\"');\n      } else {\n        values.push(key + '=' + value);\n      }\n    };\n\n    for (var key in params) print(key, params[key]);\n\n    return [name].concat(values).join('; ');\n  }\n};\n\nvar Offers = function() {\n  this._byName  = {};\n  this._inOrder = [];\n};\n\nOffers.prototype.push = function(name, params) {\n  if (!hasOwnProperty.call(this._byName, name))\n    this._byName[name] = [];\n\n  this._byName[name].push(params);\n  this._inOrder.push({ name: name, params: params });\n};\n\nOffers.prototype.eachOffer = function(callback, context) {\n  var list = this._inOrder;\n  for (var i = 0, n = list.length; i < n; i++)\n    callback.call(context, list[i].name, list[i].params);\n};\n\nOffers.prototype.byName = function(name) {\n  return this._byName[name] || [];\n};\n\nOffers.prototype.toArray = function() {\n  return this._inOrder.slice();\n};\n\nmodule.exports = Parser;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-extensions/lib/parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-extensions/lib/pipeline/cell.js":
/*!****************************************************************!*\
  !*** ./node_modules/websocket-extensions/lib/pipeline/cell.js ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Functor = __webpack_require__(/*! ./functor */ \"(ssr)/./node_modules/websocket-extensions/lib/pipeline/functor.js\"),\n    Pledge  = __webpack_require__(/*! ./pledge */ \"(ssr)/./node_modules/websocket-extensions/lib/pipeline/pledge.js\");\n\nvar Cell = function(tuple) {\n  this._ext     = tuple[0];\n  this._session = tuple[1];\n\n  this._functors = {\n    incoming: new Functor(this._session, 'processIncomingMessage'),\n    outgoing: new Functor(this._session, 'processOutgoingMessage')\n  };\n};\n\nCell.prototype.pending = function(direction) {\n  var functor = this._functors[direction];\n  if (!functor._stopped) functor.pending += 1;\n};\n\nCell.prototype.incoming = function(error, message, callback, context) {\n  this._exec('incoming', error, message, callback, context);\n};\n\nCell.prototype.outgoing = function(error, message, callback, context) {\n  this._exec('outgoing', error, message, callback, context);\n};\n\nCell.prototype.close = function() {\n  this._closed = this._closed || new Pledge();\n  this._doClose();\n  return this._closed;\n};\n\nCell.prototype._exec = function(direction, error, message, callback, context) {\n  this._functors[direction].call(error, message, function(err, msg) {\n    if (err) err.message = this._ext.name + ': ' + err.message;\n    callback.call(context, err, msg);\n    this._doClose();\n  }, this);\n};\n\nCell.prototype._doClose = function() {\n  var fin  = this._functors.incoming,\n      fout = this._functors.outgoing;\n\n  if (!this._closed || fin.pending + fout.pending !== 0) return;\n  if (this._session) this._session.close();\n  this._session = null;\n  this._closed.done();\n};\n\nmodule.exports = Cell;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-extensions/lib/pipeline/cell.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-extensions/lib/pipeline/functor.js":
/*!*******************************************************************!*\
  !*** ./node_modules/websocket-extensions/lib/pipeline/functor.js ***!
  \*******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar RingBuffer = __webpack_require__(/*! ./ring_buffer */ \"(ssr)/./node_modules/websocket-extensions/lib/pipeline/ring_buffer.js\");\n\nvar Functor = function(session, method) {\n  this._session = session;\n  this._method  = method;\n  this._queue   = new RingBuffer(Functor.QUEUE_SIZE);\n  this._stopped = false;\n  this.pending  = 0;\n};\n\nFunctor.QUEUE_SIZE = 8;\n\nFunctor.prototype.call = function(error, message, callback, context) {\n  if (this._stopped) return;\n\n  var record = { error: error, message: message, callback: callback, context: context, done: false },\n      called = false,\n      self   = this;\n\n  this._queue.push(record);\n\n  if (record.error) {\n    record.done = true;\n    this._stop();\n    return this._flushQueue();\n  }\n\n  var handler = function(err, msg) {\n    if (!(called ^ (called = true))) return;\n\n    if (err) {\n      self._stop();\n      record.error   = err;\n      record.message = null;\n    } else {\n      record.message = msg;\n    }\n\n    record.done = true;\n    self._flushQueue();\n  };\n\n  try {\n    this._session[this._method](message, handler);\n  } catch (err) {\n    handler(err);\n  }\n};\n\nFunctor.prototype._stop = function() {\n  this.pending  = this._queue.length;\n  this._stopped = true;\n};\n\nFunctor.prototype._flushQueue = function() {\n  var queue = this._queue, record;\n\n  while (queue.length > 0 && queue.peek().done) {\n    record = queue.shift();\n    if (record.error) {\n      this.pending = 0;\n      queue.clear();\n    } else {\n      this.pending -= 1;\n    }\n    record.callback.call(record.context, record.error, record.message);\n  }\n};\n\nmodule.exports = Functor;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2Vic29ja2V0LWV4dGVuc2lvbnMvbGliL3BpcGVsaW5lL2Z1bmN0b3IuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsaUJBQWlCLG1CQUFPLENBQUMsNEZBQWU7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUEsaUJBQWlCLG1GQUFtRjtBQUNwRztBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL2RlaGl4Ly4vbm9kZV9tb2R1bGVzL3dlYnNvY2tldC1leHRlbnNpb25zL2xpYi9waXBlbGluZS9mdW5jdG9yLmpzPzMzOTgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgUmluZ0J1ZmZlciA9IHJlcXVpcmUoJy4vcmluZ19idWZmZXInKTtcblxudmFyIEZ1bmN0b3IgPSBmdW5jdGlvbihzZXNzaW9uLCBtZXRob2QpIHtcbiAgdGhpcy5fc2Vzc2lvbiA9IHNlc3Npb247XG4gIHRoaXMuX21ldGhvZCAgPSBtZXRob2Q7XG4gIHRoaXMuX3F1ZXVlICAgPSBuZXcgUmluZ0J1ZmZlcihGdW5jdG9yLlFVRVVFX1NJWkUpO1xuICB0aGlzLl9zdG9wcGVkID0gZmFsc2U7XG4gIHRoaXMucGVuZGluZyAgPSAwO1xufTtcblxuRnVuY3Rvci5RVUVVRV9TSVpFID0gODtcblxuRnVuY3Rvci5wcm90b3R5cGUuY2FsbCA9IGZ1bmN0aW9uKGVycm9yLCBtZXNzYWdlLCBjYWxsYmFjaywgY29udGV4dCkge1xuICBpZiAodGhpcy5fc3RvcHBlZCkgcmV0dXJuO1xuXG4gIHZhciByZWNvcmQgPSB7IGVycm9yOiBlcnJvciwgbWVzc2FnZTogbWVzc2FnZSwgY2FsbGJhY2s6IGNhbGxiYWNrLCBjb250ZXh0OiBjb250ZXh0LCBkb25lOiBmYWxzZSB9LFxuICAgICAgY2FsbGVkID0gZmFsc2UsXG4gICAgICBzZWxmICAgPSB0aGlzO1xuXG4gIHRoaXMuX3F1ZXVlLnB1c2gocmVjb3JkKTtcblxuICBpZiAocmVjb3JkLmVycm9yKSB7XG4gICAgcmVjb3JkLmRvbmUgPSB0cnVlO1xuICAgIHRoaXMuX3N0b3AoKTtcbiAgICByZXR1cm4gdGhpcy5fZmx1c2hRdWV1ZSgpO1xuICB9XG5cbiAgdmFyIGhhbmRsZXIgPSBmdW5jdGlvbihlcnIsIG1zZykge1xuICAgIGlmICghKGNhbGxlZCBeIChjYWxsZWQgPSB0cnVlKSkpIHJldHVybjtcblxuICAgIGlmIChlcnIpIHtcbiAgICAgIHNlbGYuX3N0b3AoKTtcbiAgICAgIHJlY29yZC5lcnJvciAgID0gZXJyO1xuICAgICAgcmVjb3JkLm1lc3NhZ2UgPSBudWxsO1xuICAgIH0gZWxzZSB7XG4gICAgICByZWNvcmQubWVzc2FnZSA9IG1zZztcbiAgICB9XG5cbiAgICByZWNvcmQuZG9uZSA9IHRydWU7XG4gICAgc2VsZi5fZmx1c2hRdWV1ZSgpO1xuICB9O1xuXG4gIHRyeSB7XG4gICAgdGhpcy5fc2Vzc2lvblt0aGlzLl9tZXRob2RdKG1lc3NhZ2UsIGhhbmRsZXIpO1xuICB9IGNhdGNoIChlcnIpIHtcbiAgICBoYW5kbGVyKGVycik7XG4gIH1cbn07XG5cbkZ1bmN0b3IucHJvdG90eXBlLl9zdG9wID0gZnVuY3Rpb24oKSB7XG4gIHRoaXMucGVuZGluZyAgPSB0aGlzLl9xdWV1ZS5sZW5ndGg7XG4gIHRoaXMuX3N0b3BwZWQgPSB0cnVlO1xufTtcblxuRnVuY3Rvci5wcm90b3R5cGUuX2ZsdXNoUXVldWUgPSBmdW5jdGlvbigpIHtcbiAgdmFyIHF1ZXVlID0gdGhpcy5fcXVldWUsIHJlY29yZDtcblxuICB3aGlsZSAocXVldWUubGVuZ3RoID4gMCAmJiBxdWV1ZS5wZWVrKCkuZG9uZSkge1xuICAgIHJlY29yZCA9IHF1ZXVlLnNoaWZ0KCk7XG4gICAgaWYgKHJlY29yZC5lcnJvcikge1xuICAgICAgdGhpcy5wZW5kaW5nID0gMDtcbiAgICAgIHF1ZXVlLmNsZWFyKCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRoaXMucGVuZGluZyAtPSAxO1xuICAgIH1cbiAgICByZWNvcmQuY2FsbGJhY2suY2FsbChyZWNvcmQuY29udGV4dCwgcmVjb3JkLmVycm9yLCByZWNvcmQubWVzc2FnZSk7XG4gIH1cbn07XG5cbm1vZHVsZS5leHBvcnRzID0gRnVuY3RvcjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-extensions/lib/pipeline/functor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-extensions/lib/pipeline/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/websocket-extensions/lib/pipeline/index.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Cell   = __webpack_require__(/*! ./cell */ \"(ssr)/./node_modules/websocket-extensions/lib/pipeline/cell.js\"),\n    Pledge = __webpack_require__(/*! ./pledge */ \"(ssr)/./node_modules/websocket-extensions/lib/pipeline/pledge.js\");\n\nvar Pipeline = function(sessions) {\n  this._cells   = sessions.map(function(session) { return new Cell(session) });\n  this._stopped = { incoming: false, outgoing: false };\n};\n\nPipeline.prototype.processIncomingMessage = function(message, callback, context) {\n  if (this._stopped.incoming) return;\n  this._loop('incoming', this._cells.length - 1, -1, -1, message, callback, context);\n};\n\nPipeline.prototype.processOutgoingMessage = function(message, callback, context) {\n  if (this._stopped.outgoing) return;\n  this._loop('outgoing', 0, this._cells.length, 1, message, callback, context);\n};\n\nPipeline.prototype.close = function(callback, context) {\n  this._stopped = { incoming: true, outgoing: true };\n\n  var closed = this._cells.map(function(a) { return a.close() });\n  if (callback)\n    Pledge.all(closed).then(function() { callback.call(context) });\n};\n\nPipeline.prototype._loop = function(direction, start, end, step, message, callback, context) {\n  var cells = this._cells,\n      n     = cells.length,\n      self  = this;\n\n  while (n--) cells[n].pending(direction);\n\n  var pipe = function(index, error, msg) {\n    if (index === end) return callback.call(context, error, msg);\n\n    cells[index][direction](error, msg, function(err, m) {\n      if (err) self._stopped[direction] = true;\n      pipe(index + step, err, m);\n    });\n  };\n  pipe(start, null, message);\n};\n\nmodule.exports = Pipeline;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-extensions/lib/pipeline/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-extensions/lib/pipeline/pledge.js":
/*!******************************************************************!*\
  !*** ./node_modules/websocket-extensions/lib/pipeline/pledge.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar RingBuffer = __webpack_require__(/*! ./ring_buffer */ \"(ssr)/./node_modules/websocket-extensions/lib/pipeline/ring_buffer.js\");\n\nvar Pledge = function() {\n  this._complete  = false;\n  this._callbacks = new RingBuffer(Pledge.QUEUE_SIZE);\n};\n\nPledge.QUEUE_SIZE = 4;\n\nPledge.all = function(list) {\n  var pledge  = new Pledge(),\n      pending = list.length,\n      n       = pending;\n\n  if (pending === 0) pledge.done();\n\n  while (n--) list[n].then(function() {\n    pending -= 1;\n    if (pending === 0) pledge.done();\n  });\n  return pledge;\n};\n\nPledge.prototype.then = function(callback) {\n  if (this._complete) callback();\n  else this._callbacks.push(callback);\n};\n\nPledge.prototype.done = function() {\n  this._complete = true;\n  var callbacks = this._callbacks, callback;\n  while (callback = callbacks.shift()) callback();\n};\n\nmodule.exports = Pledge;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2Vic29ja2V0LWV4dGVuc2lvbnMvbGliL3BpcGVsaW5lL3BsZWRnZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixpQkFBaUIsbUJBQU8sQ0FBQyw0RkFBZTs7QUFFeEM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL2RlaGl4Ly4vbm9kZV9tb2R1bGVzL3dlYnNvY2tldC1leHRlbnNpb25zL2xpYi9waXBlbGluZS9wbGVkZ2UuanM/ZTkwZCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciBSaW5nQnVmZmVyID0gcmVxdWlyZSgnLi9yaW5nX2J1ZmZlcicpO1xuXG52YXIgUGxlZGdlID0gZnVuY3Rpb24oKSB7XG4gIHRoaXMuX2NvbXBsZXRlICA9IGZhbHNlO1xuICB0aGlzLl9jYWxsYmFja3MgPSBuZXcgUmluZ0J1ZmZlcihQbGVkZ2UuUVVFVUVfU0laRSk7XG59O1xuXG5QbGVkZ2UuUVVFVUVfU0laRSA9IDQ7XG5cblBsZWRnZS5hbGwgPSBmdW5jdGlvbihsaXN0KSB7XG4gIHZhciBwbGVkZ2UgID0gbmV3IFBsZWRnZSgpLFxuICAgICAgcGVuZGluZyA9IGxpc3QubGVuZ3RoLFxuICAgICAgbiAgICAgICA9IHBlbmRpbmc7XG5cbiAgaWYgKHBlbmRpbmcgPT09IDApIHBsZWRnZS5kb25lKCk7XG5cbiAgd2hpbGUgKG4tLSkgbGlzdFtuXS50aGVuKGZ1bmN0aW9uKCkge1xuICAgIHBlbmRpbmcgLT0gMTtcbiAgICBpZiAocGVuZGluZyA9PT0gMCkgcGxlZGdlLmRvbmUoKTtcbiAgfSk7XG4gIHJldHVybiBwbGVkZ2U7XG59O1xuXG5QbGVkZ2UucHJvdG90eXBlLnRoZW4gPSBmdW5jdGlvbihjYWxsYmFjaykge1xuICBpZiAodGhpcy5fY29tcGxldGUpIGNhbGxiYWNrKCk7XG4gIGVsc2UgdGhpcy5fY2FsbGJhY2tzLnB1c2goY2FsbGJhY2spO1xufTtcblxuUGxlZGdlLnByb3RvdHlwZS5kb25lID0gZnVuY3Rpb24oKSB7XG4gIHRoaXMuX2NvbXBsZXRlID0gdHJ1ZTtcbiAgdmFyIGNhbGxiYWNrcyA9IHRoaXMuX2NhbGxiYWNrcywgY2FsbGJhY2s7XG4gIHdoaWxlIChjYWxsYmFjayA9IGNhbGxiYWNrcy5zaGlmdCgpKSBjYWxsYmFjaygpO1xufTtcblxubW9kdWxlLmV4cG9ydHMgPSBQbGVkZ2U7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-extensions/lib/pipeline/pledge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-extensions/lib/pipeline/ring_buffer.js":
/*!***********************************************************************!*\
  !*** ./node_modules/websocket-extensions/lib/pipeline/ring_buffer.js ***!
  \***********************************************************************/
/***/ ((module) => {

eval("\n\nvar RingBuffer = function(bufferSize) {\n  this._bufferSize = bufferSize;\n  this.clear();\n};\n\nRingBuffer.prototype.clear = function() {\n  this._buffer     = new Array(this._bufferSize);\n  this._ringOffset = 0;\n  this._ringSize   = this._bufferSize;\n  this._head       = 0;\n  this._tail       = 0;\n  this.length      = 0;\n};\n\nRingBuffer.prototype.push = function(value) {\n  var expandBuffer = false,\n      expandRing   = false;\n\n  if (this._ringSize < this._bufferSize) {\n    expandBuffer = (this._tail === 0);\n  } else if (this._ringOffset === this._ringSize) {\n    expandBuffer = true;\n    expandRing   = (this._tail === 0);\n  }\n\n  if (expandBuffer) {\n    this._tail       = this._bufferSize;\n    this._buffer     = this._buffer.concat(new Array(this._bufferSize));\n    this._bufferSize = this._buffer.length;\n\n    if (expandRing)\n      this._ringSize = this._bufferSize;\n  }\n\n  this._buffer[this._tail] = value;\n  this.length += 1;\n  if (this._tail < this._ringSize) this._ringOffset += 1;\n  this._tail = (this._tail + 1) % this._bufferSize;\n};\n\nRingBuffer.prototype.peek = function() {\n  if (this.length === 0) return void 0;\n  return this._buffer[this._head];\n};\n\nRingBuffer.prototype.shift = function() {\n  if (this.length === 0) return void 0;\n\n  var value = this._buffer[this._head];\n  this._buffer[this._head] = void 0;\n  this.length -= 1;\n  this._ringOffset -= 1;\n\n  if (this._ringOffset === 0 && this.length > 0) {\n    this._head       = this._ringSize;\n    this._ringOffset = this.length;\n    this._ringSize   = this._bufferSize;\n  } else {\n    this._head = (this._head + 1) % this._ringSize;\n  }\n  return value;\n};\n\nmodule.exports = RingBuffer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-extensions/lib/pipeline/ring_buffer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-extensions/lib/websocket_extensions.js":
/*!***********************************************************************!*\
  !*** ./node_modules/websocket-extensions/lib/websocket_extensions.js ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Parser   = __webpack_require__(/*! ./parser */ \"(ssr)/./node_modules/websocket-extensions/lib/parser.js\"),\n    Pipeline = __webpack_require__(/*! ./pipeline */ \"(ssr)/./node_modules/websocket-extensions/lib/pipeline/index.js\");\n\nvar Extensions = function() {\n  this._rsv1 = this._rsv2 = this._rsv3 = null;\n\n  this._byName   = {};\n  this._inOrder  = [];\n  this._sessions = [];\n  this._index    = {};\n};\n\nExtensions.MESSAGE_OPCODES = [1, 2];\n\nvar instance = {\n  add: function(ext) {\n    if (typeof ext.name !== 'string') throw new TypeError('extension.name must be a string');\n    if (ext.type !== 'permessage') throw new TypeError('extension.type must be \"permessage\"');\n\n    if (typeof ext.rsv1 !== 'boolean') throw new TypeError('extension.rsv1 must be true or false');\n    if (typeof ext.rsv2 !== 'boolean') throw new TypeError('extension.rsv2 must be true or false');\n    if (typeof ext.rsv3 !== 'boolean') throw new TypeError('extension.rsv3 must be true or false');\n\n    if (this._byName.hasOwnProperty(ext.name))\n      throw new TypeError('An extension with name \"' + ext.name + '\" is already registered');\n\n    this._byName[ext.name] = ext;\n    this._inOrder.push(ext);\n  },\n\n  generateOffer: function() {\n    var sessions = [],\n        offer    = [],\n        index    = {};\n\n    this._inOrder.forEach(function(ext) {\n      var session = ext.createClientSession();\n      if (!session) return;\n\n      var record = [ext, session];\n      sessions.push(record);\n      index[ext.name] = record;\n\n      var offers = session.generateOffer();\n      offers = offers ? [].concat(offers) : [];\n\n      offers.forEach(function(off) {\n        offer.push(Parser.serializeParams(ext.name, off));\n      }, this);\n    }, this);\n\n    this._sessions = sessions;\n    this._index    = index;\n\n    return offer.length > 0 ? offer.join(', ') : null;\n  },\n\n  activate: function(header) {\n    var responses = Parser.parseHeader(header),\n        sessions  = [];\n\n    responses.eachOffer(function(name, params) {\n      var record = this._index[name];\n\n      if (!record)\n        throw new Error('Server sent an extension response for unknown extension \"' + name + '\"');\n\n      var ext      = record[0],\n          session  = record[1],\n          reserved = this._reserved(ext);\n\n      if (reserved)\n        throw new Error('Server sent two extension responses that use the RSV' +\n                        reserved[0] + ' bit: \"' +\n                        reserved[1] + '\" and \"' + ext.name + '\"');\n\n      if (session.activate(params) !== true)\n        throw new Error('Server sent unacceptable extension parameters: ' +\n                        Parser.serializeParams(name, params));\n\n      this._reserve(ext);\n      sessions.push(record);\n    }, this);\n\n    this._sessions = sessions;\n    this._pipeline = new Pipeline(sessions);\n  },\n\n  generateResponse: function(header) {\n    var sessions = [],\n        response = [],\n        offers   = Parser.parseHeader(header);\n\n    this._inOrder.forEach(function(ext) {\n      var offer = offers.byName(ext.name);\n      if (offer.length === 0 || this._reserved(ext)) return;\n\n      var session = ext.createServerSession(offer);\n      if (!session) return;\n\n      this._reserve(ext);\n      sessions.push([ext, session]);\n      response.push(Parser.serializeParams(ext.name, session.generateResponse()));\n    }, this);\n\n    this._sessions = sessions;\n    this._pipeline = new Pipeline(sessions);\n\n    return response.length > 0 ? response.join(', ') : null;\n  },\n\n  validFrameRsv: function(frame) {\n    var allowed = { rsv1: false, rsv2: false, rsv3: false },\n        ext;\n\n    if (Extensions.MESSAGE_OPCODES.indexOf(frame.opcode) >= 0) {\n      for (var i = 0, n = this._sessions.length; i < n; i++) {\n        ext = this._sessions[i][0];\n        allowed.rsv1 = allowed.rsv1 || ext.rsv1;\n        allowed.rsv2 = allowed.rsv2 || ext.rsv2;\n        allowed.rsv3 = allowed.rsv3 || ext.rsv3;\n      }\n    }\n\n    return (allowed.rsv1 || !frame.rsv1) &&\n           (allowed.rsv2 || !frame.rsv2) &&\n           (allowed.rsv3 || !frame.rsv3);\n  },\n\n  processIncomingMessage: function(message, callback, context) {\n    this._pipeline.processIncomingMessage(message, callback, context);\n  },\n\n  processOutgoingMessage: function(message, callback, context) {\n    this._pipeline.processOutgoingMessage(message, callback, context);\n  },\n\n  close: function(callback, context) {\n    if (!this._pipeline) return callback.call(context);\n    this._pipeline.close(callback, context);\n  },\n\n  _reserve: function(ext) {\n    this._rsv1 = this._rsv1 || (ext.rsv1 && ext.name);\n    this._rsv2 = this._rsv2 || (ext.rsv2 && ext.name);\n    this._rsv3 = this._rsv3 || (ext.rsv3 && ext.name);\n  },\n\n  _reserved: function(ext) {\n    if (this._rsv1 && ext.rsv1) return [1, this._rsv1];\n    if (this._rsv2 && ext.rsv2) return [2, this._rsv2];\n    if (this._rsv3 && ext.rsv3) return [3, this._rsv3];\n    return false;\n  }\n};\n\nfor (var key in instance)\n  Extensions.prototype[key] = instance[key];\n\nmodule.exports = Extensions;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2Vic29ja2V0LWV4dGVuc2lvbnMvbGliL3dlYnNvY2tldF9leHRlbnNpb25zLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLGVBQWUsbUJBQU8sQ0FBQyx5RUFBVTtBQUNqQyxlQUFlLG1CQUFPLENBQUMsbUZBQVk7O0FBRW5DO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLOztBQUVMO0FBQ0E7O0FBRUE7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsS0FBSzs7QUFFTDtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxLQUFLOztBQUVMO0FBQ0E7O0FBRUE7QUFDQSxHQUFHOztBQUVIO0FBQ0Esb0JBQW9CLHVDQUF1QztBQUMzRDs7QUFFQTtBQUNBLGlEQUFpRCxPQUFPO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZWhpeC8uL25vZGVfbW9kdWxlcy93ZWJzb2NrZXQtZXh0ZW5zaW9ucy9saWIvd2Vic29ja2V0X2V4dGVuc2lvbnMuanM/NDk5YSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciBQYXJzZXIgICA9IHJlcXVpcmUoJy4vcGFyc2VyJyksXG4gICAgUGlwZWxpbmUgPSByZXF1aXJlKCcuL3BpcGVsaW5lJyk7XG5cbnZhciBFeHRlbnNpb25zID0gZnVuY3Rpb24oKSB7XG4gIHRoaXMuX3JzdjEgPSB0aGlzLl9yc3YyID0gdGhpcy5fcnN2MyA9IG51bGw7XG5cbiAgdGhpcy5fYnlOYW1lICAgPSB7fTtcbiAgdGhpcy5faW5PcmRlciAgPSBbXTtcbiAgdGhpcy5fc2Vzc2lvbnMgPSBbXTtcbiAgdGhpcy5faW5kZXggICAgPSB7fTtcbn07XG5cbkV4dGVuc2lvbnMuTUVTU0FHRV9PUENPREVTID0gWzEsIDJdO1xuXG52YXIgaW5zdGFuY2UgPSB7XG4gIGFkZDogZnVuY3Rpb24oZXh0KSB7XG4gICAgaWYgKHR5cGVvZiBleHQubmFtZSAhPT0gJ3N0cmluZycpIHRocm93IG5ldyBUeXBlRXJyb3IoJ2V4dGVuc2lvbi5uYW1lIG11c3QgYmUgYSBzdHJpbmcnKTtcbiAgICBpZiAoZXh0LnR5cGUgIT09ICdwZXJtZXNzYWdlJykgdGhyb3cgbmV3IFR5cGVFcnJvcignZXh0ZW5zaW9uLnR5cGUgbXVzdCBiZSBcInBlcm1lc3NhZ2VcIicpO1xuXG4gICAgaWYgKHR5cGVvZiBleHQucnN2MSAhPT0gJ2Jvb2xlYW4nKSB0aHJvdyBuZXcgVHlwZUVycm9yKCdleHRlbnNpb24ucnN2MSBtdXN0IGJlIHRydWUgb3IgZmFsc2UnKTtcbiAgICBpZiAodHlwZW9mIGV4dC5yc3YyICE9PSAnYm9vbGVhbicpIHRocm93IG5ldyBUeXBlRXJyb3IoJ2V4dGVuc2lvbi5yc3YyIG11c3QgYmUgdHJ1ZSBvciBmYWxzZScpO1xuICAgIGlmICh0eXBlb2YgZXh0LnJzdjMgIT09ICdib29sZWFuJykgdGhyb3cgbmV3IFR5cGVFcnJvcignZXh0ZW5zaW9uLnJzdjMgbXVzdCBiZSB0cnVlIG9yIGZhbHNlJyk7XG5cbiAgICBpZiAodGhpcy5fYnlOYW1lLmhhc093blByb3BlcnR5KGV4dC5uYW1lKSlcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ0FuIGV4dGVuc2lvbiB3aXRoIG5hbWUgXCInICsgZXh0Lm5hbWUgKyAnXCIgaXMgYWxyZWFkeSByZWdpc3RlcmVkJyk7XG5cbiAgICB0aGlzLl9ieU5hbWVbZXh0Lm5hbWVdID0gZXh0O1xuICAgIHRoaXMuX2luT3JkZXIucHVzaChleHQpO1xuICB9LFxuXG4gIGdlbmVyYXRlT2ZmZXI6IGZ1bmN0aW9uKCkge1xuICAgIHZhciBzZXNzaW9ucyA9IFtdLFxuICAgICAgICBvZmZlciAgICA9IFtdLFxuICAgICAgICBpbmRleCAgICA9IHt9O1xuXG4gICAgdGhpcy5faW5PcmRlci5mb3JFYWNoKGZ1bmN0aW9uKGV4dCkge1xuICAgICAgdmFyIHNlc3Npb24gPSBleHQuY3JlYXRlQ2xpZW50U2Vzc2lvbigpO1xuICAgICAgaWYgKCFzZXNzaW9uKSByZXR1cm47XG5cbiAgICAgIHZhciByZWNvcmQgPSBbZXh0LCBzZXNzaW9uXTtcbiAgICAgIHNlc3Npb25zLnB1c2gocmVjb3JkKTtcbiAgICAgIGluZGV4W2V4dC5uYW1lXSA9IHJlY29yZDtcblxuICAgICAgdmFyIG9mZmVycyA9IHNlc3Npb24uZ2VuZXJhdGVPZmZlcigpO1xuICAgICAgb2ZmZXJzID0gb2ZmZXJzID8gW10uY29uY2F0KG9mZmVycykgOiBbXTtcblxuICAgICAgb2ZmZXJzLmZvckVhY2goZnVuY3Rpb24ob2ZmKSB7XG4gICAgICAgIG9mZmVyLnB1c2goUGFyc2VyLnNlcmlhbGl6ZVBhcmFtcyhleHQubmFtZSwgb2ZmKSk7XG4gICAgICB9LCB0aGlzKTtcbiAgICB9LCB0aGlzKTtcblxuICAgIHRoaXMuX3Nlc3Npb25zID0gc2Vzc2lvbnM7XG4gICAgdGhpcy5faW5kZXggICAgPSBpbmRleDtcblxuICAgIHJldHVybiBvZmZlci5sZW5ndGggPiAwID8gb2ZmZXIuam9pbignLCAnKSA6IG51bGw7XG4gIH0sXG5cbiAgYWN0aXZhdGU6IGZ1bmN0aW9uKGhlYWRlcikge1xuICAgIHZhciByZXNwb25zZXMgPSBQYXJzZXIucGFyc2VIZWFkZXIoaGVhZGVyKSxcbiAgICAgICAgc2Vzc2lvbnMgID0gW107XG5cbiAgICByZXNwb25zZXMuZWFjaE9mZmVyKGZ1bmN0aW9uKG5hbWUsIHBhcmFtcykge1xuICAgICAgdmFyIHJlY29yZCA9IHRoaXMuX2luZGV4W25hbWVdO1xuXG4gICAgICBpZiAoIXJlY29yZClcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdTZXJ2ZXIgc2VudCBhbiBleHRlbnNpb24gcmVzcG9uc2UgZm9yIHVua25vd24gZXh0ZW5zaW9uIFwiJyArIG5hbWUgKyAnXCInKTtcblxuICAgICAgdmFyIGV4dCAgICAgID0gcmVjb3JkWzBdLFxuICAgICAgICAgIHNlc3Npb24gID0gcmVjb3JkWzFdLFxuICAgICAgICAgIHJlc2VydmVkID0gdGhpcy5fcmVzZXJ2ZWQoZXh0KTtcblxuICAgICAgaWYgKHJlc2VydmVkKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1NlcnZlciBzZW50IHR3byBleHRlbnNpb24gcmVzcG9uc2VzIHRoYXQgdXNlIHRoZSBSU1YnICtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc2VydmVkWzBdICsgJyBiaXQ6IFwiJyArXG4gICAgICAgICAgICAgICAgICAgICAgICByZXNlcnZlZFsxXSArICdcIiBhbmQgXCInICsgZXh0Lm5hbWUgKyAnXCInKTtcblxuICAgICAgaWYgKHNlc3Npb24uYWN0aXZhdGUocGFyYW1zKSAhPT0gdHJ1ZSlcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdTZXJ2ZXIgc2VudCB1bmFjY2VwdGFibGUgZXh0ZW5zaW9uIHBhcmFtZXRlcnM6ICcgK1xuICAgICAgICAgICAgICAgICAgICAgICAgUGFyc2VyLnNlcmlhbGl6ZVBhcmFtcyhuYW1lLCBwYXJhbXMpKTtcblxuICAgICAgdGhpcy5fcmVzZXJ2ZShleHQpO1xuICAgICAgc2Vzc2lvbnMucHVzaChyZWNvcmQpO1xuICAgIH0sIHRoaXMpO1xuXG4gICAgdGhpcy5fc2Vzc2lvbnMgPSBzZXNzaW9ucztcbiAgICB0aGlzLl9waXBlbGluZSA9IG5ldyBQaXBlbGluZShzZXNzaW9ucyk7XG4gIH0sXG5cbiAgZ2VuZXJhdGVSZXNwb25zZTogZnVuY3Rpb24oaGVhZGVyKSB7XG4gICAgdmFyIHNlc3Npb25zID0gW10sXG4gICAgICAgIHJlc3BvbnNlID0gW10sXG4gICAgICAgIG9mZmVycyAgID0gUGFyc2VyLnBhcnNlSGVhZGVyKGhlYWRlcik7XG5cbiAgICB0aGlzLl9pbk9yZGVyLmZvckVhY2goZnVuY3Rpb24oZXh0KSB7XG4gICAgICB2YXIgb2ZmZXIgPSBvZmZlcnMuYnlOYW1lKGV4dC5uYW1lKTtcbiAgICAgIGlmIChvZmZlci5sZW5ndGggPT09IDAgfHwgdGhpcy5fcmVzZXJ2ZWQoZXh0KSkgcmV0dXJuO1xuXG4gICAgICB2YXIgc2Vzc2lvbiA9IGV4dC5jcmVhdGVTZXJ2ZXJTZXNzaW9uKG9mZmVyKTtcbiAgICAgIGlmICghc2Vzc2lvbikgcmV0dXJuO1xuXG4gICAgICB0aGlzLl9yZXNlcnZlKGV4dCk7XG4gICAgICBzZXNzaW9ucy5wdXNoKFtleHQsIHNlc3Npb25dKTtcbiAgICAgIHJlc3BvbnNlLnB1c2goUGFyc2VyLnNlcmlhbGl6ZVBhcmFtcyhleHQubmFtZSwgc2Vzc2lvbi5nZW5lcmF0ZVJlc3BvbnNlKCkpKTtcbiAgICB9LCB0aGlzKTtcblxuICAgIHRoaXMuX3Nlc3Npb25zID0gc2Vzc2lvbnM7XG4gICAgdGhpcy5fcGlwZWxpbmUgPSBuZXcgUGlwZWxpbmUoc2Vzc2lvbnMpO1xuXG4gICAgcmV0dXJuIHJlc3BvbnNlLmxlbmd0aCA+IDAgPyByZXNwb25zZS5qb2luKCcsICcpIDogbnVsbDtcbiAgfSxcblxuICB2YWxpZEZyYW1lUnN2OiBmdW5jdGlvbihmcmFtZSkge1xuICAgIHZhciBhbGxvd2VkID0geyByc3YxOiBmYWxzZSwgcnN2MjogZmFsc2UsIHJzdjM6IGZhbHNlIH0sXG4gICAgICAgIGV4dDtcblxuICAgIGlmIChFeHRlbnNpb25zLk1FU1NBR0VfT1BDT0RFUy5pbmRleE9mKGZyYW1lLm9wY29kZSkgPj0gMCkge1xuICAgICAgZm9yICh2YXIgaSA9IDAsIG4gPSB0aGlzLl9zZXNzaW9ucy5sZW5ndGg7IGkgPCBuOyBpKyspIHtcbiAgICAgICAgZXh0ID0gdGhpcy5fc2Vzc2lvbnNbaV1bMF07XG4gICAgICAgIGFsbG93ZWQucnN2MSA9IGFsbG93ZWQucnN2MSB8fCBleHQucnN2MTtcbiAgICAgICAgYWxsb3dlZC5yc3YyID0gYWxsb3dlZC5yc3YyIHx8IGV4dC5yc3YyO1xuICAgICAgICBhbGxvd2VkLnJzdjMgPSBhbGxvd2VkLnJzdjMgfHwgZXh0LnJzdjM7XG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIChhbGxvd2VkLnJzdjEgfHwgIWZyYW1lLnJzdjEpICYmXG4gICAgICAgICAgIChhbGxvd2VkLnJzdjIgfHwgIWZyYW1lLnJzdjIpICYmXG4gICAgICAgICAgIChhbGxvd2VkLnJzdjMgfHwgIWZyYW1lLnJzdjMpO1xuICB9LFxuXG4gIHByb2Nlc3NJbmNvbWluZ01lc3NhZ2U6IGZ1bmN0aW9uKG1lc3NhZ2UsIGNhbGxiYWNrLCBjb250ZXh0KSB7XG4gICAgdGhpcy5fcGlwZWxpbmUucHJvY2Vzc0luY29taW5nTWVzc2FnZShtZXNzYWdlLCBjYWxsYmFjaywgY29udGV4dCk7XG4gIH0sXG5cbiAgcHJvY2Vzc091dGdvaW5nTWVzc2FnZTogZnVuY3Rpb24obWVzc2FnZSwgY2FsbGJhY2ssIGNvbnRleHQpIHtcbiAgICB0aGlzLl9waXBlbGluZS5wcm9jZXNzT3V0Z29pbmdNZXNzYWdlKG1lc3NhZ2UsIGNhbGxiYWNrLCBjb250ZXh0KTtcbiAgfSxcblxuICBjbG9zZTogZnVuY3Rpb24oY2FsbGJhY2ssIGNvbnRleHQpIHtcbiAgICBpZiAoIXRoaXMuX3BpcGVsaW5lKSByZXR1cm4gY2FsbGJhY2suY2FsbChjb250ZXh0KTtcbiAgICB0aGlzLl9waXBlbGluZS5jbG9zZShjYWxsYmFjaywgY29udGV4dCk7XG4gIH0sXG5cbiAgX3Jlc2VydmU6IGZ1bmN0aW9uKGV4dCkge1xuICAgIHRoaXMuX3JzdjEgPSB0aGlzLl9yc3YxIHx8IChleHQucnN2MSAmJiBleHQubmFtZSk7XG4gICAgdGhpcy5fcnN2MiA9IHRoaXMuX3JzdjIgfHwgKGV4dC5yc3YyICYmIGV4dC5uYW1lKTtcbiAgICB0aGlzLl9yc3YzID0gdGhpcy5fcnN2MyB8fCAoZXh0LnJzdjMgJiYgZXh0Lm5hbWUpO1xuICB9LFxuXG4gIF9yZXNlcnZlZDogZnVuY3Rpb24oZXh0KSB7XG4gICAgaWYgKHRoaXMuX3JzdjEgJiYgZXh0LnJzdjEpIHJldHVybiBbMSwgdGhpcy5fcnN2MV07XG4gICAgaWYgKHRoaXMuX3JzdjIgJiYgZXh0LnJzdjIpIHJldHVybiBbMiwgdGhpcy5fcnN2Ml07XG4gICAgaWYgKHRoaXMuX3JzdjMgJiYgZXh0LnJzdjMpIHJldHVybiBbMywgdGhpcy5fcnN2M107XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG59O1xuXG5mb3IgKHZhciBrZXkgaW4gaW5zdGFuY2UpXG4gIEV4dGVuc2lvbnMucHJvdG90eXBlW2tleV0gPSBpbnN0YW5jZVtrZXldO1xuXG5tb2R1bGUuZXhwb3J0cyA9IEV4dGVuc2lvbnM7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-extensions/lib/websocket_extensions.js\n");

/***/ })

};
;